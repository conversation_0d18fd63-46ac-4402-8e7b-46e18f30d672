
.pdf-container {
  font-family: 'Inter', sans-serif;
  padding: 20px;
  color: #333;
}

.pdf-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #2563eb;
}

.pdf-header h1 {
  color: #1e40af;
  font-size: 24px;
  margin-bottom: 10px;
}

.pdf-content {
  margin: 20px 0;
}

.pdf-table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
}

.pdf-table th,
.pdf-table td {
  padding: 12px;
  text-align: left;
  border: 1px solid #e5e7eb;
}

.pdf-table th {
  background-color: #f3f4f6;
  font-weight: 600;
}

.pdf-table tr:nth-child(even) {
  background-color: #f9fafb;
}

.pdf-recommendation {
  background-color: #f0f9ff;
  border-left: 4px solid #2563eb;
  padding: 15px;
  margin: 20px 0;
}

.tax-burden-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin: 20px 0;
}

.tax-burden-card {
  background: #f8fafc;
  padding: 15px;
  border-radius: 8px;
}

.structure-analysis {
  margin: 20px 0;
  padding: 15px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.structure-metrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-top: 10px;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.chart-wrapper {
  margin: 20px 0;
  text-align: center;
}

.chart-image {
  max-width: 100%;
  height: auto;
}

.considerations {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin: 20px 0;
}

.consideration {
  background: #f8fafc;
  padding: 15px;
  border-radius: 8px;
}

.pdf-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 15px;
  border-top: 1px solid #e5e7eb;
  color: #6b7280;
  font-size: 12px;
}
.comparison-table-section {
  page-break-inside: avoid;
}

.chart-section {
  page-break-inside: avoid;
}

.analysis-section {
  page-break-inside: avoid;
}

.recommendation-card {
  page-break-inside: avoid;
}

.tax-burden-grid {
  page-break-inside: avoid;
}

.breakpoints-grid {
  page-break-inside: avoid;
}
