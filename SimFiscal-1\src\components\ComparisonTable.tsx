// src/components/ComparisonTable.tsx
import React from 'react';
import { SimulationResult } from '@types';
import { formatCurrency, formatPercent } from '../utils/formatters';

interface ComparisonTableProps {
  results: SimulationResult[];
}

const ComparisonTable: React.FC<ComparisonTableProps> = ({ results }) => {
  if (!results || results.length === 0) return null;

  // Formatar o nome da estrutura
  const formatStructureName = (structure: string, variant?: string) => {
    const name = structure
      .replace('ENI_', 'ENI ')
      .replace('SociedadePorQuotas', 'SOCIEDADE POR QUOTAS');

    return name.toUpperCase();
  };

  return (
    <div className="overflow-x-auto rounded-xl shadow-lg">
      <table className="min-w-full bg-white border-collapse">
        <thead>
          <tr className="bg-gradient-to-r from-blue-600 to-blue-700">
            <th className="py-4 px-6 text-left font-bold text-white border-b border-blue-500">INDICADOR</th>
            {results.map((result, index) => {
              // Formatar o nome da estrutura de forma mais clara
              const structureName = result.structure
                .replace('ENI_Simplified', 'ENI Simplificado')
                .replace('ENI_Organized', 'ENI Contabilidade Organizada')
                .replace('SociedadePorQuotas', 'Sociedade por Quotas')
                .replace('Unipessoal', 'Sociedade Unipessoal');

              // Extrair apenas a parte relevante da variante (sem "Recomendado: ")
              const variantText = result.variant ? result.variant.replace('Recomendado: ', '') : '';

              // Adicionar número de referência para cada simulação
              const simulationNumber = index + 1;

              return (
                <th key={index} className={`py-4 px-6 text-center font-bold text-white border-b border-blue-500 ${result.recommended ? 'bg-gradient-to-r from-green-600 to-green-700' : ''}`}>
                  <div className="flex items-center justify-center mb-2">
                    <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center text-xl font-bold">
                      {simulationNumber}
                    </div>
                  </div>
                  <div className="text-base">{structureName}</div>
                  {variantText && (
                    <div className="text-xs font-normal mt-1 text-blue-100">
                      {variantText}
                    </div>
                  )}
                  {result.recommended && (
                    <div className="mt-2 inline-block px-2 py-1 bg-white/20 rounded-full text-xs font-semibold">
                      Recomendado
                    </div>
                  )}
                </th>
              );
            })}
          </tr>
        </thead>
        <tbody>
          {/* Grupo: Rendimentos e Custos */}
          <tr className="bg-gray-100">
            <td colSpan={results.length + 1} className="py-3 px-6 font-bold text-blue-700 border-b">
              Rendimentos e Custos
            </td>
          </tr>

          {/* Faturação Anual */}
          <tr className="hover:bg-blue-50 transition-colors">
            <td className="py-4 px-6 border-b border-gray-200 font-medium text-gray-700">Faturação Anual</td>
            {results.map((result, index) => (
              <td key={index} className={`py-4 px-6 text-center border-b border-gray-200 ${result.recommended ? 'bg-green-50' : ''}`}>
                {formatCurrency(result.taxableProfit + (result.annualCosts || 0))}
              </td>
            ))}
          </tr>

          {/* Custos Dedutíveis */}
          <tr className="hover:bg-blue-50 transition-colors">
            <td className="py-4 px-6 border-b border-gray-200 font-medium text-gray-700">Custos Dedutíveis</td>
            {results.map((result, index) => (
              <td key={index} className={`py-4 px-6 text-center border-b border-gray-200 ${result.recommended ? 'bg-green-50' : ''}`}>
                {formatCurrency(result.annualCosts || 0)}
              </td>
            ))}
          </tr>

          {/* Lucro Tributável */}
          <tr className="hover:bg-blue-50 transition-colors">
            <td className="py-4 px-6 border-b border-gray-200 font-medium text-gray-700">Lucro Tributável</td>
            {results.map((result, index) => (
              <td key={index} className={`py-4 px-6 text-center border-b border-gray-200 font-semibold ${result.recommended ? 'bg-green-50' : ''}`}>
                {formatCurrency(result.taxableProfit)}
              </td>
            ))}
          </tr>

          {/* Grupo: Impostos e Contribuições */}
          <tr className="bg-gray-100">
            <td colSpan={results.length + 1} className="py-3 px-6 font-bold text-blue-700 border-b">
              Impostos e Contribuições
            </td>
          </tr>

          {/* Imposto (IRS/IRC) */}
          <tr className="hover:bg-blue-50 transition-colors">
            <td className="py-4 px-6 border-b border-gray-200 font-medium text-gray-700">Imposto (IRS/IRC)</td>
            {results.map((result, index) => (
              <td key={index} className={`py-4 px-6 text-center border-b border-gray-200 text-red-600 font-medium ${result.recommended ? 'bg-green-50' : ''}`}>
                {formatCurrency(result.incomeTax)}
              </td>
            ))}
          </tr>

          {/* Segurança Social */}
          <tr className="hover:bg-blue-50 transition-colors">
            <td className="py-4 px-6 border-b border-gray-200 font-medium text-gray-700">Segurança Social</td>
            {results.map((result, index) => (
              <td key={index} className={`py-4 px-6 text-center border-b border-gray-200 text-red-600 font-medium ${result.recommended ? 'bg-green-50' : ''}`}>
                {formatCurrency(result.socialSecurity)}
              </td>
            ))}
          </tr>

          {/* Retenção na Fonte */}
          {results.some(result => result.withholdingTax && result.withholdingTax > 0) && (
            <tr className="hover:bg-blue-50 transition-colors">
              <td className="py-4 px-6 border-b border-gray-200 font-medium text-gray-700">Retenção na Fonte</td>
              {results.map((result, index) => (
                <td key={index} className={`py-4 px-6 text-center border-b border-gray-200 text-orange-600 font-medium ${result.recommended ? 'bg-green-50' : ''}`}>
                  {formatCurrency(result.withholdingTax || 0)}
                </td>
              ))}
            </tr>
          )}

          {/* Grupo: IVA */}
          <tr className="bg-gray-100">
            <td colSpan={results.length + 1} className="py-3 px-6 font-bold text-blue-700 border-b">
              IVA
            </td>
          </tr>

          {/* IVA Liquidado */}
          <tr className="hover:bg-blue-50 transition-colors">
            <td className="py-4 px-6 border-b border-gray-200 font-medium text-gray-700">IVA Liquidado</td>
            {results.map((result, index) => (
              <td key={index} className={`py-4 px-6 text-center border-b border-gray-200 ${result.recommended ? 'bg-green-50' : ''}`}>
                {formatCurrency(result.vatPaid)}
              </td>
            ))}
          </tr>

          {/* IVA Dedutível */}
          <tr className="hover:bg-blue-50 transition-colors">
            <td className="py-4 px-6 border-b border-gray-200 font-medium text-gray-700">IVA Dedutível</td>
            {results.map((result, index) => (
              <td key={index} className={`py-4 px-6 text-center border-b border-gray-200 ${result.recommended ? 'bg-green-50' : ''}`}>
                {formatCurrency(result.vatDeductible)}
              </td>
            ))}
          </tr>

          {/* IVA a Pagar */}
          <tr className="hover:bg-blue-50 transition-colors">
            <td className="py-4 px-6 border-b border-gray-200 font-medium text-gray-700">IVA a Pagar</td>
            {results.map((result, index) => (
              <td key={index} className={`py-4 px-6 text-center border-b border-gray-200 text-red-600 font-medium ${result.recommended ? 'bg-green-50' : ''}`}>
                {formatCurrency(result.vatPaid - result.vatDeductible)}
              </td>
            ))}
          </tr>


          {/* Grupo: Resultados Finais */}
          <tr className="bg-gray-100">
            <td colSpan={results.length + 1} className="py-3 px-6 font-bold text-blue-700 border-b">
              Resultados Finais
            </td>
          </tr>

          {/* Carga Fiscal Total */}
          <tr className="hover:bg-blue-50 transition-colors">
            <td className="py-4 px-6 border-b border-gray-200 font-medium text-gray-700">Carga Fiscal Total</td>
            {results.map((result, index) => (
              <td key={index} className={`py-4 px-6 text-center border-b border-gray-200 text-red-600 font-semibold ${result.recommended ? 'bg-green-50' : ''}`}>
                {formatCurrency(result.totalTax)}
              </td>
            ))}
          </tr>

          {/* Lucro Líquido */}
          <tr className="bg-blue-50 hover:bg-blue-100 transition-colors">
            <td className="py-5 px-6 border-b border-gray-200 font-bold text-gray-800 text-lg">Lucro Líquido da Empresa</td>
            {results.map((result, index) => (
              <td key={index} className={`py-5 px-6 text-center border-b border-gray-200 font-bold text-lg ${result.recommended ? 'bg-green-100' : ''}`}>
                <span className={result.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}>
                  {formatCurrency(result.netProfit)}
                </span>
              </td>
            ))}
          </tr>

          {/* Rendimento Líquido Total - Removido */}

          {/* Taxa Efetiva */}
          <tr className="hover:bg-blue-50 transition-colors">
            <td className="py-4 px-6 border-b border-gray-200 font-medium text-gray-700">Taxa Efetiva de Imposto</td>
            {results.map((result, index) => (
              <td key={index} className={`py-4 px-6 text-center border-b border-gray-200 font-medium ${result.recommended ? 'bg-green-50' : ''}`}>
                {formatPercent(result.effectiveTaxRate || 0)}
              </td>
            ))}
          </tr>

          {/* Grupo: Detalhes de Remuneração (se aplicável) */}
          {(results.some(r => r.grossSalary) || results.some(r => r.netSalary)) && (
            <tr className="bg-gray-100">
              <td colSpan={results.length + 1} className="py-3 px-6 font-bold text-blue-700 border-b">
                Detalhes de Remuneração
              </td>
            </tr>
          )}

          {/* Linhas adicionais para detalhes de salário, se disponíveis */}
          {results.some(r => r.grossSalary) && (
            <tr className="hover:bg-blue-50 transition-colors">
              <td className="py-4 px-6 border-b border-gray-200 font-medium text-gray-700">Salário Bruto Anual</td>
              {results.map((result, index) => (
                <td key={index} className={`py-4 px-6 text-center border-b border-gray-200 ${result.recommended ? 'bg-green-50' : ''}`}>
                  {formatCurrency(result.grossSalary || 0)}
                </td>
              ))}
            </tr>
          )}

          {results.some(r => r.netSalary) && (
            <tr className="hover:bg-blue-50 transition-colors">
              <td className="py-4 px-6 border-b border-gray-200 font-medium text-gray-700">Salário Líquido Anual</td>
              {results.map((result, index) => (
                <td key={index} className={`py-4 px-6 text-center border-b border-gray-200 text-green-600 font-medium ${result.recommended ? 'bg-green-50' : ''}`}>
                  {formatCurrency(result.netSalary || 0)}
                </td>
              ))}
            </tr>
          )}


        </tbody>
      </table>
    </div>
  );
};

export default ComparisonTable;
