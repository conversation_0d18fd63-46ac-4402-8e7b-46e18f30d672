// src/components/SalaryOptimizationOptions.tsx
import React from 'react';
import { FiscalParams } from '@types';

interface SalaryOptimizationOptionsProps {
  params: FiscalParams;
  onChange: (params: FiscalParams) => void;
  showOptions: boolean;
}

const SalaryOptimizationOptions: React.FC<SalaryOptimizationOptionsProps> = ({
  params,
  onChange,
  showOptions
}) => {
  if (!showOptions) return null;



  const handleSalaryMonthlyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    onChange({
      ...params,
      salaryMonthly: isNaN(value) ? 0 : value
    });
  };

  const handleSalaryDistributionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange({
      ...params,
      salaryDistribution: e.target.value as 'optimal' | 'custom'
    });
  };

  const handleSalaryRatioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) / 100;
    onChange({
      ...params,
      salaryRatio: isNaN(value) ? 0 : Math.min(1, Math.max(0, value))
    });
  };

  const handleOtherSocialSecurityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({
      ...params,
      hasOtherSocialSecurity: e.target.checked
    });
  };

  const handleWithholdingTaxRateChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = parseFloat(e.target.value);
    onChange({
      ...params,
      withholdingTaxRate: isNaN(value) ? 0 : value
    });
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md mb-6">
      <h3 className="text-lg font-semibold mb-4 text-blue-800">Opções de Otimização Salarial</h3>

      <div className="mb-4">
        <p className="mt-1 text-sm text-gray-700 font-medium">
          Configure o salário mensal para os sócios-gerentes
        </p>
      </div>

      {(
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="salaryMonthly" className="block text-sm font-medium text-gray-700">
              Salário Mensal (€)
            </label>
            <input
              type="number"
              id="salaryMonthly"
              value={params.salaryMonthly || 0}
              onChange={handleSalaryMonthlyChange}
              min="0"
              step="100"
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
            <p className="mt-1 text-xs text-gray-500">
              Salário bruto mensal para sócios-gerentes
            </p>
          </div>
        </div>
      )}

      {/* Campos adicionais para Segurança Social e Retenção na Fonte */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6 pt-6 border-t border-gray-200">
        <div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="hasOtherSocialSecurity"
              checked={params.hasOtherSocialSecurity || false}
              onChange={handleOtherSocialSecurityChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="hasOtherSocialSecurity" className="ml-2 block text-sm font-medium text-gray-700">
              Já desconta Segurança Social noutro local
            </label>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Se já desconta SS como trabalhador por conta de outrem ou noutro regime
          </p>
        </div>

        <div>
          <label htmlFor="withholdingTaxRate" className="block text-sm font-medium text-gray-700">
            Taxa de Retenção na Fonte (%)
          </label>
          <select
            id="withholdingTaxRate"
            value={params.withholdingTaxRate || 0}
            onChange={handleWithholdingTaxRateChange}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value={0}>Sem retenção na fonte</option>
            <option value={11.5}>11,5% - Atividades não listadas</option>
            <option value={16.5}>16,5% - Propriedade intelectual</option>
            <option value={20}>20% - Profissionais não residentes</option>
            <option value={23}>23% - Taxa padrão (2025)</option>
            <option value={25}>25% - Taxa opcional</option>
          </select>
          <p className="mt-1 text-xs text-gray-500">
            Taxa aplicável aos seus rendimentos como trabalhador independente
          </p>
        </div>
      </div>

    </div>
  );
};

export default SalaryOptimizationOptions;
