// src/services/salaryOptimizer.ts

import { FiscalParams, SimulationResult } from '../types';
import {
  IRS_BRACKETS,
  SS_RATES,
  IRC_RATE,
  IRC_RATE_SME,
  IRC_SME_THRESHOLD,
  IAS,
  SOLIDARITY_TAX_RATES,
  SS_SERVICE_COEFFICIENT
} from '@types/TaxBrackets2025';

/**
 * Calcula o IRS para um determinado rendimento anual
 */
function calculateIRS(annualIncome: number): number {
  const bracket = IRS_BRACKETS.find(b => annualIncome >= b.min && annualIncome <= b.max);
  if (!bracket) return 0;

  // Cálculo do IRS base
  const baseIRS = (annualIncome * bracket.rate) - bracket.deduction;

  // Cálculo da Taxa Adicional de Solidariedade, se aplicável
  const solidarityTax = calculateSolidarityTax(annualIncome);

  return baseIRS + solidarityTax;
}

/**
 * Calcula a Taxa Adicional de Solidariedade
 */
function calculateSolidarityTax(annualIncome: number): number {
  let solidarityTax = 0;

  // Aplicar a Taxa Adicional de Solidariedade para rendimentos elevados
  for (const bracket of SOLIDARITY_TAX_RATES) {
    if (annualIncome > bracket.min) {
      const taxableAmount = Math.min(annualIncome, bracket.max) - bracket.min;
      solidarityTax += taxableAmount * bracket.rate;
    }
  }

  return solidarityTax;
}

/**
 * Calcula o IRC para um determinado lucro tributável
 */
function calculateIRC(taxableProfit: number): number {
  if (taxableProfit <= IRC_SME_THRESHOLD) {
    return taxableProfit * IRC_RATE_SME; // Taxa reduzida para PMEs (16%)
  } else {
    return (IRC_SME_THRESHOLD * IRC_RATE_SME) +
           ((taxableProfit - IRC_SME_THRESHOLD) * IRC_RATE);
  }
}

/**
 * Calcula a Segurança Social do empregado
 */
function calculateEmployeeSS(grossSalary: number): number {
  return grossSalary * SS_RATES.employee;
}

/**
 * Calcula a Segurança Social do empregador
 */
function calculateEmployerSS(grossSalary: number): number {
  return grossSalary * SS_RATES.company;
}

// Função removida - dividendos não são mais utilizados

/**
 * Calcula o resultado para uma sociedade unipessoal com um determinado salário mensal
 */
export function calculateUnipessoalWithSalary(
  params: FiscalParams,
  monthlySalary: number
): SimulationResult {
  // Faturação anual (rendimento bruto)
  const annualRevenue = params.annualRevenue;

  // Custos operacionais dedutíveis (excluindo salários)
  const operationalCosts = params.annualCosts;

  // Lucro antes de salários e impostos
  const profitBeforeSalaries = annualRevenue - operationalCosts;

  // Cálculo do salário anual (14 meses incluindo subsídios)
  const annualSalary = monthlySalary * 14;

  // Segurança Social do empregador (23.75%)
  const employerSS = calculateEmployerSS(annualSalary);

  // Custo total com salário (incluindo SS do empregador)
  const totalSalaryCost = annualSalary + employerSS;

  // Lucro tributável para IRC (após dedução dos custos com salários)
  const taxableProfit = Math.max(0, profitBeforeSalaries - totalSalaryCost);

  // IRC sobre o lucro tributável
  const corporateTax = calculateIRC(taxableProfit);

  // IRS sobre o salário
  const salaryTax = calculateIRS(annualSalary);

  // Segurança Social do empregado (11%)
  const employeeSS = calculateEmployeeSS(annualSalary);

  // Salário líquido após IRS e SS do empregado
  const netSalary = annualSalary - salaryTax - employeeSS;

  // Total de impostos e contribuições
  const totalTax = corporateTax + salaryTax + employerSS + employeeSS;

  // Lucro líquido final (faturação - custos operacionais - impostos)
  // Nota: O salário já está incluído nos custos e impostos, não deve ser subtraído novamente
  const netProfit = annualRevenue - operationalCosts - totalSalaryCost - corporateTax;

  // Taxa efetiva de imposto (sobre o lucro antes de salários)
  const effectiveTaxRate = profitBeforeSalaries > 0 ? (totalTax / profitBeforeSalaries) * 100 : 0;

  return {
    structure: 'Unipessoal',
    variant: `Salário: ${monthlySalary.toLocaleString('pt-PT')}€/mês`,
    taxableProfit: profitBeforeSalaries, // Lucro antes de salários (para comparação entre cenários)
    incomeTax: corporateTax,
    socialSecurity: employerSS + employeeSS,
    employerSocialSecurity: employerSS,
    employeeSocialSecurity: employeeSS,
    grossSalary: annualSalary,
    netSalary,
    vatPaid: 0, // Será calculado separadamente
    vatDeductible: 0, // Será calculado separadamente
    totalTax,
    netProfit: netProfit, // Agora usando o cálculo correto
    effectiveTaxRate,
    annualCosts: operationalCosts,
  };
}

/**
 * Encontra o salário ótimo para maximizar o rendimento líquido
 */
export function findOptimalSalary(params: FiscalParams): SimulationResult {
  const taxableProfit = params.annualRevenue - params.annualCosts;

  // Definir intervalo de salários mensais a testar
  // Começamos com salário mínimo e vamos até um valor razoável
  const minSalary = 870; // Salário mínimo nacional 2025
  const maxSalary = Math.min(10000, taxableProfit / 14); // Limitado pelo lucro disponível

  // Incremento para teste (100€)
  const increment = 100;

  let bestResult: SimulationResult | null = null;
  let bestNetIncome = 0;

  // Testar diferentes valores de salário
  for (let monthlySalary = minSalary; monthlySalary <= maxSalary; monthlySalary += increment) {
    const result = calculateUnipessoalWithSalary(params, monthlySalary);

    if (result.netProfit > bestNetIncome) {
      bestNetIncome = result.netProfit;
      bestResult = result;
    }
  }

  // Refinar a busca em torno do melhor valor encontrado
  if (bestResult && bestResult.grossSalary) {
    const bestMonthlySalary = bestResult.grossSalary / 14;
    const refinedMin = Math.max(minSalary, bestMonthlySalary - increment);
    const refinedMax = Math.min(maxSalary, bestMonthlySalary + increment);

    // Incremento mais fino (10€)
    const refinedIncrement = 10;

    for (let monthlySalary = refinedMin; monthlySalary <= refinedMax; monthlySalary += refinedIncrement) {
      const result = calculateUnipessoalWithSalary(params, monthlySalary);

      if (result.netProfit > bestNetIncome) {
        bestNetIncome = result.netProfit;
        bestResult = result;
      }
    }
  }

  if (bestResult) {
    bestResult.recommended = true;
    bestResult.variant = `Salário Otimizado: ${(bestResult.grossSalary! / 14).toLocaleString('pt-PT')}€/mês`;
  }

  return bestResult || calculateUnipessoalWithSalary(params, minSalary);
}

/**
 * Gera múltiplas simulações com diferentes distribuições de salário
 */
export function generateSalaryDistributionSimulations(params: FiscalParams): SimulationResult[] {
  const results: SimulationResult[] = [];
  const taxableProfit = params.annualRevenue - params.annualCosts;

  // Salário ótimo
  const optimalResult = findOptimalSalary(params);
  results.push(optimalResult);

  // Calcular opções de salário com base no lucro tributável
  const baseMonthlyIncome = Math.max(870, Math.min(3000, Math.round(taxableProfit / 14 / 1.5)));

  // Adicionar algumas distribuições predefinidas para comparação
  const salaryOptions = [
    { label: 'Salário Mínimo', value: 870 },
    { label: 'Salário Moderado', value: Math.round(baseMonthlyIncome * 0.7) },
    { label: 'Salário Elevado', value: Math.round(baseMonthlyIncome * 1.3) },
    { label: 'Apenas Dividendos', value: 0 }
  ];

  for (const option of salaryOptions) {
    // Evitar duplicar o salário ótimo
    if (optimalResult.grossSalary &&
        Math.abs((optimalResult.grossSalary / 14) - option.value) < 100) {
      continue;
    }

    const result = calculateUnipessoalWithSalary(params, option.value);
    result.variant = `${option.label}: ${option.value.toLocaleString('pt-PT')}€/mês`;
    results.push(result);
  }

  return results;
}

/**
 * Calcula o resultado para uma sociedade por quotas com múltiplos sócios
 */
export function calculateSociedadePorQuotasWithSalaries(
  params: FiscalParams,
  monthlySalaries: number[]
): SimulationResult {
  // Faturação anual (rendimento bruto)
  const annualRevenue = params.annualRevenue;

  // Custos operacionais dedutíveis (excluindo salários)
  const operationalCosts = params.annualCosts;

  // Lucro antes de salários e impostos
  const profitBeforeSalaries = annualRevenue - operationalCosts;

  // Total de salários anuais (14 meses incluindo subsídios)
  const annualSalaries = monthlySalaries.map(s => s * 14);
  const totalAnnualSalary = annualSalaries.reduce((sum, s) => sum + s, 0);

  // Segurança Social do empregador (23.75%)
  const employerSS = calculateEmployerSS(totalAnnualSalary);

  // Custo total com salários (incluindo SS do empregador)
  const totalSalaryCost = totalAnnualSalary + employerSS;

  // Lucro tributável para IRC (após dedução dos custos com salários)
  const taxableProfit = Math.max(0, profitBeforeSalaries - totalSalaryCost);

  // IRC sobre o lucro tributável
  const corporateTax = calculateIRC(taxableProfit);

  // Removido cálculo de dividendos

  // IRS sobre os salários (calculado individualmente para cada sócio)
  const salaryTaxes = annualSalaries.map(s => calculateIRS(s));
  const totalSalaryTax = salaryTaxes.reduce((sum, t) => sum + t, 0);

  // Segurança Social dos empregados (11%)
  const employeeSS = calculateEmployeeSS(totalAnnualSalary);

  // Salários líquidos após IRS e SS dos empregados
  const netSalary = totalAnnualSalary - totalSalaryTax - employeeSS;

  // Total de impostos e contribuições
  const totalTax = corporateTax + totalSalaryTax + employerSS + employeeSS;

  // Lucro líquido final (faturação - custos operacionais - impostos)
  // Nota: O salário já está incluído nos custos e impostos, não deve ser subtraído novamente
  const netProfit = annualRevenue - operationalCosts - totalSalaryCost - corporateTax;

  // Taxa efetiva de imposto (sobre o lucro antes de salários)
  const effectiveTaxRate = profitBeforeSalaries > 0 ? (totalTax / profitBeforeSalaries) * 100 : 0;

  return {
    structure: 'SociedadePorQuotas',
    variant: `${monthlySalaries.length} sócio(s) com salário`,
    taxableProfit: profitBeforeSalaries, // Lucro antes de salários (para comparação entre cenários)
    incomeTax: corporateTax,
    socialSecurity: employerSS + employeeSS,
    employerSocialSecurity: employerSS,
    employeeSocialSecurity: employeeSS,
    grossSalary: totalAnnualSalary,
    netSalary,
    vatPaid: 0, // Será calculado separadamente
    vatDeductible: 0, // Será calculado separadamente
    totalTax,
    netProfit: netProfit, // Agora usando o cálculo correto
    effectiveTaxRate,
    annualCosts: operationalCosts,
  };
}
