// src/tests/calculator.test.ts
import { calculateAll } from '../services/calculator';
import { FiscalParams } from '../types';

describe('Calculator Service', () => {
  test('Deve calcular corretamente para ENI Simplificado com valores de 2025', () => {
    const params: FiscalParams = {
      legalStructures: ['ENI_Simplified'],
      annualRevenue: 50000,
      annualCosts: 10000,
      activityType: 'services',
      vatType: 'normal',
      vatRegime: 'normal',
      hasOtherSocialSecurity: false
    };

    const results = calculateAll(params);
    expect(results.length).toBe(1);

    const eniResult = results[0];
    expect(eniResult.structure).toBe('ENI_Simplified');

    // Verificar se o coeficiente de serviços está correto (70% para 2025)
    expect(eniResult.serviceCoefficient).toBe(0.7);

    // Verificar se o limiar de isenção de IVA está correto (14500€ para 2025)
    expect(eniResult.vatThreshold).toBe(14500);

    // Verificar se o cálculo do IRS está correto
    const taxableProfit = 50000 * 0.7; // 35000€
    expect(eniResult.taxableProfit).toBeCloseTo(taxableProfit);

    // Verificar se o cálculo da Segurança Social está correto
    // Verificar apenas se o valor é maior que zero
    expect(eniResult.socialSecurity).toBeGreaterThan(0);

    // Verificar se o valor está dentro de uma faixa razoável
    // Para um rendimento de 35000€, a SS deve estar entre 4000€ e 8000€
    expect(eniResult.socialSecurity).toBeGreaterThan(4000);
    expect(eniResult.socialSecurity).toBeLessThan(8000);
  });

  test('Deve calcular corretamente para Sociedade Unipessoal com valores de 2025', () => {
    const params: FiscalParams = {
      legalStructures: ['Unipessoal'],
      annualRevenue: 60000,
      annualCosts: 15000,
      activityType: 'services',
      vatType: 'normal',
      vatRegime: 'normal',
      hasOtherSocialSecurity: false
    };

    const results = calculateAll(params);
    const unipessoalResult = results.find(r => r.structure === 'Unipessoal');
    expect(unipessoalResult).toBeDefined();

    if (unipessoalResult) {
      // Verificar se o cálculo do IRC está correto
      // Até 50.000€ aplica-se a taxa de 16%, acima disso 20%
      const taxableProfit = 60000 - 15000; // 45000€
      expect(unipessoalResult.taxableProfit).toBeCloseTo(taxableProfit);

      // Como o lucro tributável é menor que 50.000€, aplica-se a taxa de 16%
      const expectedIRC = taxableProfit * 0.16;
      expect(unipessoalResult.incomeTax).toBeCloseTo(expectedIRC);

      // Verificar se o limiar de isenção de IVA está correto (14500€ para 2025)
      expect(unipessoalResult.vatThreshold).toBe(14500);
    }
  });

  test('Deve calcular corretamente a Taxa Adicional de Solidariedade para rendimentos elevados', () => {
    const params: FiscalParams = {
      legalStructures: ['ENI_Simplified'],
      annualRevenue: 150000,
      annualCosts: 20000,
      activityType: 'services',
      vatType: 'normal',
      vatRegime: 'normal',
      hasOtherSocialSecurity: false
    };

    const results = calculateAll(params);
    const eniResult = results[0];

    // Rendimento relevante: 150000 * 0.7 = 105000€
    // Este valor está acima de 80000€, então deve ter Taxa Adicional de Solidariedade
    // (105000 - 80000) * 0.025 = 625€

    // Verificar se o IRS inclui a Taxa Adicional de Solidariedade
    expect(eniResult.solidarityTax).toBeGreaterThan(0);
  });
});
