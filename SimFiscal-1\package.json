{"name": "react-typescript", "version": "1.0.0", "type": "module", "description": "React TypeScript on Replit, using Vite bundler", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint --config .eslintrc.cjs --ext .ts,.tsx \"src\" --no-ignore", "format": "prettier --write \"src/**/*.{js,ts,tsx,json,css,md}\"", "test": "jest"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "@vitejs/plugin-react": "^2.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.3.3", "typescript": "^4.7.4", "vite": "^3.0.4"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "chart.js": "^4.4.9", "html2pdf.js": "^0.10.3", "react-chartjs-2": "^5.3.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-router-dom": "^6.22.0", "recharts": "^2.15.2", "yup": "^1.6.1"}}