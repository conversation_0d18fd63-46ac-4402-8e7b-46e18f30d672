
import type { FiscalParams } from '@types/FiscalParams';
import type { SalaryDividendOptimization, TaxBreakpoint } from '@types/HistoricalData';
import { calculateAll } from './calculator';

export function findTaxBreakpoints(params: FiscalParams): TaxBreakpoint[] {
  const breakpoints: TaxBreakpoint[] = [];
  const steps = 20;
  const maxRevenue = params.annualRevenue * 2;

  for (let revenue = 0; revenue <= maxRevenue; revenue += maxRevenue/steps) {
    const testParams = { ...params, annualRevenue: revenue };
    const results = calculateAll(testParams);
    const taxRates = results.map(r => r.totalTax / r.taxableProfit);

    // Identify significant changes in tax rates
    if (taxRates.some((rate, i, arr) => i > 0 && Math.abs(rate - arr[i-1]) > 0.05)) {
      breakpoints.push({
        value: revenue,
        impact: `Mudança significativa na taxa efetiva de imposto`,
        description: `Ponto de alteração na ${revenue.toLocaleString('pt-PT')}€`
      });
    }
  }

  return breakpoints;
}

// Função removida - dividendos não são mais utilizados

export function projectMultiYear(params: FiscalParams, years: number, growthRate: number): MultiYearProjection[] {
  return Array.from({ length: years }, (_, i) => {
    const year = new Date().getFullYear() + i;
    const revenue = params.annualRevenue * Math.pow(1 + growthRate, i);
    const costs = params.annualCosts * Math.pow(1 + growthRate, i);
    const yearParams = { ...params, annualRevenue: revenue, annualCosts: costs };

    return {
      year,
      revenue,
      costs,
      results: calculateAll(yearParams)
    };
  });
}
