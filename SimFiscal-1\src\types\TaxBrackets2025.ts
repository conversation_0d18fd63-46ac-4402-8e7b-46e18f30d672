// Interface para as taxas de IVA
export interface VATRate {
  type: 'reduced' | 'intermediate' | 'normal';
  rate: number;
}

// Escalões de IRS para 2025 (valores oficiais atualizados)
export const IRS_BRACKETS_2025 = [
  { min: 0, max: 8059, rate: 0.125, deduction: 0 },
  { min: 8059, max: 12160, rate: 0.16, deduction: 282.07 },
  { min: 12160, max: 17233, rate: 0.215, deduction: 950.91 },
  { min: 17233, max: 22306, rate: 0.244, deduction: 1450.67 },
  { min: 22306, max: 28400, rate: 0.314, deduction: 3011.98 },
  { min: 28400, max: 41629, rate: 0.349, deduction: 4006.10 },
  { min: 41629, max: 44987, rate: 0.431, deduction: 7419.54 },
  { min: 44987, max: 83696, rate: 0.446, deduction: 8094.51 },
  { min: 83696, max: Infinity, rate: 0.48, deduction: 10939.90 }
];

// Usar os escalões de 2025 como padrão
export const IRS_BRACKETS = IRS_BRACKETS_2025;

// Valor do IAS (Indexante dos Apoios Sociais) para 2025
export const IAS = 522.50;

// Limiares de isenção de IVA para 2025
export const VAT_EXEMPTION_THRESHOLD = 15000; // Novo limiar base para 2025
export const VAT_IMMEDIATE_TRANSITION_THRESHOLD = 18750; // 125% do limiar base (obriga à transição imediata)

export const VAT_RATES: VATRate[] = [
  { type: 'reduced', rate: 0.06 },
  { type: 'intermediate', rate: 0.13 },
  { type: 'normal', rate: 0.23 }
];

// Coeficientes do regime simplificado para IRS
export const SERVICE_COEFFICIENT = 0.75; // Prestação de serviços (IRS - 75%)
export const SALES_COEFFICIENT = 0.15; // Vendas de mercadorias e produtos (IRS - 15%)
export const LOCAL_ACCOMMODATION_COEFFICIENT = 0.35; // Alojamento local (moradia e apartamento)
export const FOOD_HOSPITALITY_COEFFICIENT = 0.15; // Restauração, bebidas e hotelaria (IRS - 15%)

// Coeficientes para Segurança Social (diferentes dos coeficientes de IRS)
export const SS_SERVICE_COEFFICIENT = 0.70; // Prestação de serviços (SS - 70%)
export const SS_SALES_COEFFICIENT = 0.20; // Produção/venda de bens (SS - 20%)
export const SS_FOOD_HOSPITALITY_COEFFICIENT = 0.20; // Hotelaria/restauração/bebidas (SS - 20%)

// Taxas de Retenção na Fonte para 2025
export const WITHHOLDING_TAX_RATES = {
  standard: 0.23, // Taxa padrão para trabalhadores independentes (23%)
  optional: 0.25, // Taxa opcional (25%)
  unlisted: 0.115, // Atividades não listadas na Portaria n.º 1011/2001 (11,5%)
  intellectual: 0.165, // Propriedade intelectual/industrial (16,5%)
  nonResident: 0.20 // Profissionais não residentes (20%)
};

// Valor mínimo de Segurança Social para sócios sem salário (1 IAS)
export const SS_MINIMUM_PARTNER_CONTRIBUTION = IAS; // €522,50 em 2025

// Taxas de IRC
export const IRC_RATE = 0.20; // Taxa geral para 2025
export const IRC_RATE_SME = 0.16; // Taxa para PMEs (até 50.000€)
export const IRC_SME_THRESHOLD = 50000; // Limite para aplicação da taxa reduzida

// Taxas de Segurança Social atualizadas para 2025 (valores oficiais)
export const SS_RATES = {
  independent: 0.214, // Taxa para trabalhadores independentes (21,4%)
  independent_reduced: 0.214, // Taxa para trabalhadores independentes
  independent_entrepreneur: 0.252, // Empresário em nome individual (25,2%)
  company: 0.2375, // Taxa da entidade empregadora
  employee: 0.11 // Taxa do trabalhador por conta de outrem
};

// Taxa Adicional de Solidariedade
export const SOLIDARITY_TAX_RATES = [
  { min: 80000, max: 250000, rate: 0.025 },
  { min: 250000, max: Infinity, rate: 0.05 }
];
