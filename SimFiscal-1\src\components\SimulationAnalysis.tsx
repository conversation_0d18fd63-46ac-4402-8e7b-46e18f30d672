import React from 'react';
import type { SimulationResult } from '@types/SimulationResult';

interface Props {
  results: SimulationResult[];
}

export function SimulationAnalysis({ results }: Props) {
  if (!results.length) return null;

  const bestScenario = results.reduce((prev, current) =>
    prev.netProfit > current.netProfit ? prev : current
  );

  const taxBurdens = results.map(r => ({
    structure: r.structure,
    burden: ((r.totalTax / r.taxableProfit) * 100).toFixed(1)
  }));

  const fmt = new Intl.NumberFormat('pt-PT', { style: 'currency', currency: 'EUR' });

  return (
    <div className="analysis-container">
      <div className="analysis-section">
        <h3 className="section-title">Recomendação Principal</h3>
        <div className="recommendation-card">
          <div className="recommendation-header">
            <span className="structure-badge">{bestScenario.structure.replace('_', ' ')}</span>
            <span className="profit-value">{fmt.format(bestScenario.netProfit)}</span>
          </div>
          <p className="recommendation-text">
            Esta estrutura apresenta o melhor resultado financeiro baseado nos valores simulados.
          </p>
        </div>
      </div>

      <div className="analysis-section">
        <h3 className="section-title">Carga Fiscal</h3>
        <div className="tax-burden-grid">
          {taxBurdens.map(burden => (
            <div key={burden.structure} className="tax-burden-card">
              <span className="structure-name">{burden.structure.replace('_', ' ')}</span>
              <span className="burden-value">{burden.burden}%</span>
            </div>
          ))}
        </div>
      </div>

      <div className="analysis-section">
        <h3 className="section-title">Análise de Breakpoints</h3>
        <div className="breakpoints-grid">
          {/* Breakpoint 1: Limite IVA */}
          <div className="breakpoint-card">
            <span className="breakpoint-title">Limite IVA</span>
            <span className="breakpoint-value">€18.750</span>
            <p>Limite para regime de isenção do artigo 53º</p>
          </div>

          {/* Breakpoint 2: Coeficientes */}
          <div className="breakpoint-card">
            <span className="breakpoint-title">Coeficientes</span>
            <span className="breakpoint-value">{results[0].structure.includes('ENI') ? '0.75' : '0.25'}</span>
            <p>Coeficiente aplicado ao rendimento bruto</p>
          </div>

          {/* Breakpoint 3: SS */}
          <div className="breakpoint-card">
            <span className="breakpoint-title">Segurança Social</span>
            <span className="breakpoint-value">€7.843</span>
            <p>Valor mínimo anual para cálculo das contribuições SS (corresponde a 1.5 x IAS x 12)</p>
          </div>
        </div>
      </div>
    </div>
  );
}