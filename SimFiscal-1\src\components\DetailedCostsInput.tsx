// src/components/DetailedCostsInput.tsx
import React, { useEffect } from 'react';
import { DetailedCosts, CostCategory } from '@types/DetailedCosts';

interface DetailedCostsInputProps {
  costs: DetailedCosts;
  onChange: (costs: DetailedCosts) => void;
}

const DetailedCostsInput: React.FC<DetailedCostsInputProps> = ({ costs, onChange }) => {
  // Função para atualizar um valor mensal e recalcular o valor anual
  const handleMonthlyValueChange = (key: keyof DetailedCosts, value: number) => {
    const annualValue = value * 12;
    const updatedCosts = {
      ...costs,
      [key]: {
        ...costs[key],
        monthlyValue: value,
        annualValue: annualValue
      }
    };
    onChange(updatedCosts);
  };

  // Função para atualizar um valor anual e recalcular o valor mensal
  const handleAnnualValueChange = (key: keyof DetailedCosts, value: number) => {
    const monthlyValue = value / 12;
    const updatedCosts = {
      ...costs,
      [key]: {
        ...costs[key],
        monthlyValue: monthlyValue,
        annualValue: value
      }
    };
    onChange(updatedCosts);
  };

  // Renderiza uma linha da tabela para uma categoria de custo
  const renderCostRow = (key: keyof DetailedCosts, category: CostCategory) => {
    return (
      <tr key={key} className="border-b border-gray-200 hover:bg-gray-50">
        <td className="py-3 px-4 text-right">
          <input
            type="number"
            value={category.monthlyValue || ''}
            onChange={(e) => handleMonthlyValueChange(key, parseFloat(e.target.value) || 0)}
            className="w-24 px-2 py-1 border rounded-md text-right"
            placeholder="0.00"
            step="0.01"
          />
        </td>
        <td className="py-3 px-4 text-right">
          <input
            type="number"
            value={category.annualValue || ''}
            onChange={(e) => handleAnnualValueChange(key, parseFloat(e.target.value) || 0)}
            className="w-32 px-2 py-1 border rounded-md text-right"
            placeholder="0.00"
            step="0.01"
          />
        </td>
        <td className="py-3 px-4">{category.description}</td>
        <td className="py-3 px-4 text-center">
          {typeof category.vatDeductible === 'string' 
            ? category.vatDeductible 
            : category.vatDeductible ? 'Sim' : 'Não'}
        </td>
      </tr>
    );
  };

  // Agrupar as categorias de custos por tipo
  const costGroups = [
    {
      title: 'Imobilizado',
      keys: ['vehicleAssets', 'otherAssets'] as const
    },
    {
      title: 'Serviços',
      keys: ['securityServices', 'marketingServices', 'greenReceipts', 'specializedServices', 'legalServices', 'accountingServices'] as const
    },
    {
      title: 'Custos Bancários',
      keys: ['bankFees'] as const
    },
    {
      title: 'Manutenção',
      keys: ['maintenanceRepair', 'construction'] as const
    },
    {
      title: 'Materiais e Consumíveis',
      keys: ['toolsUtensils', 'decorationItems', 'booksDocumentation', 'officeSupplies', 'giftItems'] as const
    },
    {
      title: 'Energia e Utilidades',
      keys: ['electricity', 'gas', 'fuel'] as const
    },
    {
      title: 'Deslocações e Estadas',
      keys: ['travelAccommodation', 'travelTickets', 'travelTolls', 'otherTransport'] as const
    },
    {
      title: 'Rendas e Alugueres',
      keys: ['rentals'] as const
    },
    {
      title: 'Comunicação',
      keys: ['mobilePhone', 'internet', 'cableTv', 'courierServices'] as const
    },
    {
      title: 'Seguros',
      keys: ['insurance'] as const
    },
    {
      title: 'Administrativos',
      keys: ['notaryFees'] as const
    },
    {
      title: 'Higiene e Limpeza',
      keys: ['cleaningSupplies'] as const
    },
    {
      title: 'Pessoal',
      keys: ['salaries', 'socialSecurity', 'bonuses', 'mealAllowance', 'trainingEvents', 'educationAllowance', 'otherSalaryComponents'] as const
    },
    {
      title: 'Outros',
      keys: ['donations', 'christmasItems', 'giftCards', 'footwear', 'hairdressing', 'clothing', 'groceriesCleaning', 'groceriesOffice', 'groceriesHome'] as const
    }
  ];

  // Calcular o total dos custos
  const totalMonthly = Object.values(costs).reduce((sum, category) => sum + category.monthlyValue, 0);
  const totalAnnual = Object.values(costs).reduce((sum, category) => sum + category.annualValue, 0);

  return (
    <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
      <h3 className="text-xl font-bold mb-6 text-blue-700 border-b pb-3 border-blue-100">
        Listagem Detalhada de Custos
      </h3>
      
      <div className="overflow-x-auto">
        <table className="min-w-full bg-white border border-gray-200 rounded-lg">
          <thead>
            <tr className="bg-blue-800 text-white">
              <th className="py-3 px-4 text-center">MÊS</th>
              <th className="py-3 px-4 text-center">ANO</th>
              <th className="py-3 px-4 text-left">Descrição</th>
              <th className="py-3 px-4 text-center">IVA dedutível</th>
            </tr>
          </thead>
          <tbody>
            {costGroups.map((group) => (
              <React.Fragment key={group.title}>
                <tr className="bg-blue-100">
                  <td colSpan={4} className="py-2 px-4 font-semibold text-blue-800">
                    {group.title}
                  </td>
                </tr>
                {group.keys.map((key) => renderCostRow(key, costs[key]))}
              </React.Fragment>
            ))}
            <tr className="bg-gray-100 font-bold">
              <td className="py-3 px-4 text-right">{totalMonthly.toFixed(2)} €</td>
              <td className="py-3 px-4 text-right">{totalAnnual.toFixed(2)} €</td>
              <td className="py-3 px-4">TOTAL</td>
              <td className="py-3 px-4"></td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div className="mt-6 bg-blue-50 p-4 rounded-lg">
        <div className="flex items-center">
          <svg className="h-6 w-6 text-blue-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h4 className="font-semibold text-blue-800">Custos Anuais Totais: {totalAnnual.toFixed(2)} €</h4>
            <p className="text-sm text-blue-700 mt-1">
              Este valor será utilizado nos cálculos fiscais. Certifique-se de incluir todos os custos relevantes para a sua atividade.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailedCostsInput;
