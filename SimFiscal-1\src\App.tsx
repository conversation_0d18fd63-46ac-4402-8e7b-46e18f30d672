
import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { MainLayout } from './layouts/MainLayout';
import { SimulationPage } from './pages/SimulationPage';
import { ResultsPage } from './pages/ResultsPage';
import FiscalInfoPage from './pages/FiscalInfoPage';
import './App.css';

export default function App() {
  return (
    <BrowserRouter basename="/">
      <Routes>
        <Route path="/" element={<MainLayout />}>
          <Route index element={<SimulationPage />} />
          <Route path="simulation" element={<SimulationPage />} />
          <Route path="results" element={<ResultsPage />} />
          <Route path="fiscal-info" element={<FiscalInfoPage />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}
