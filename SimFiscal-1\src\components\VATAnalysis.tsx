
import React from 'react';
import type { SimulationResult } from '@types/SimulationResult';

interface Props {
  results: SimulationResult[];
}

export function VATAnalysis({ results }: Props) {
  const calculateVATEfficiency = (result: SimulationResult) => {
    return (result.vatDeductible / result.vatPaid) * 100;
  };

  const getVATRecommendation = (efficiency: number) => {
    if (efficiency < 30) return "Considere rever a estrutura de custos para melhorar dedução de IVA";
    if (efficiency < 60) return "Eficiência de IVA moderada - oportunidade de otimização";
    return "Boa eficiência de IVA";
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border mt-6">
      <h3 className="text-xl font-medium mb-4">Análise de IVA</h3>
      {results[0]?.vatRegime === 'exempt53' && (
        <div className="bg-blue-50 p-4 rounded-md mb-4">
          <p className="text-sm text-blue-800">
            Regime Art.º 53.º – isenção até €{VAT_EXEMPTION_THRESHOLD.toLocaleString('pt-PT')}
            {results[0].annualRevenue > VAT_EXEMPTION_THRESHOLD && (
              <>; IVA aplicado sobre {(results[0].annualRevenue - VAT_EXEMPTION_THRESHOLD).toLocaleString('pt-PT')}€ de excesso</>
            )}
          </p>
        </div>
      )}
      {results.map(result => (
        <div key={result.structure} className="mb-4">
          <h4 className="font-medium">{result.structure.replace('_', ' ')}</h4>
          <div className="grid grid-cols-2 gap-4 mt-2">
            <div>
              <p className="text-sm text-gray-600">Eficiência IVA</p>
              <p className="text-lg font-medium">{calculateVATEfficiency(result).toFixed(1)}%</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Recomendação</p>
              <p className="text-sm">{getVATRecommendation(calculateVATEfficiency(result))}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
