
export interface OptimizationScenario {
  salaryRatio: number;
  dividendsRatio: number;
  totalCompensation: number;
  netTakeHome: number;
  taxEfficiency: number;
}

export interface TaxThreshold {
  type: 'VAT' | 'SS' | 'IRS';
  value: number;
  description: string;
  impact: string;
}

export interface OptimizationResult {
  bestScenario: OptimizationScenario;
  thresholdAlerts: TaxThreshold[];
  recommendations: string[];
}
