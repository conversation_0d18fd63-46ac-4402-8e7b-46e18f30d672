import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Tooltip,
  Legend,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import type { SimulationResult } from '@types/SimulationResult';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Tooltip, Legend);

interface Props {
  data: SimulationResult[];
}

export function LineChart({ data }: Props) {
  const labels = data.map((r) => r.structure.replace('_', ' '));
  const net = data.map((r) => r.netProfit);

  const chartData = {
    labels,
    datasets: [
      {
        label: 'Lucro Líquido',
        data: net,
        fill: false,
        borderColor: 'rgba(75,192,192,1)',
        tension: 0.4,
      },
    ],
  };

  const options = {
    responsive: true,
    scales: {
      y: {
        beginAtZero: true,
      }
    },
    plugins: {
      tooltip: {
        callbacks: {
          label: (ctx) =>
            new Intl.NumberFormat('pt-PT', { style: 'currency', currency: 'EUR' }).format(
              ctx.parsed.y,
            ),
        },
      },
      legend: { position: 'top' as const },
    },
  };

  return <Line options={options} data={chartData} />;
}
