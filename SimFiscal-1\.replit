run = "npm run dev"
modules = ["nodejs-20"]
hidden = [".config", "tsconfig.json", "tsconfig.node.json", "vite.config.js", ".gitignore"]
entrypoint = "src/App.tsx"

[nix]
channel = "stable-24_05"

[unitTest]
language = "nodejs"

[deployment]
deploymentTarget = "static"
build = ["npm", "run", "build"]
publicDir = "dist"

[[ports]]
localPort = 5173
externalPort = 80

[[ports]]
localPort = 5174
externalPort = 3000

[workflows]
runButton = "Run Dev Server"

[[workflows.workflow]]
name = "Run Dev Server"
author = 41570282
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev -- --host 0.0.0.0"
