// src/components/FiscalDashboard.tsx
import React from 'react';
import { SimulationResult } from '@types';
import { formatCurrency, formatPercent } from '../utils/formatters';

interface FiscalDashboardProps {
  results: SimulationResult[];
}

const FiscalDashboard: React.FC<FiscalDashboardProps> = ({ results }) => {
  if (!results || results.length === 0) return null;

  // Encontrar a estrutura mais vantajosa
  const bestResult = results.reduce(
    (prev, current) => (prev.netProfit > current.netProfit) ? prev : current
  );

  // Encontrar a estrutura menos vantajosa
  const worstResult = results.reduce(
    (prev, current) => (prev.netProfit < current.netProfit) ? prev : current
  );

  // Calcular a economia fiscal
  const taxSaving = bestResult.netProfit - worstResult.netProfit;

  // Formatar o nome da estrutura
  const formatStructureName = (structure: string) => {
    return structure
      .replace('ENI_', 'ENI ')
      .replace('SociedadePorQuotas', 'Sociedade por Quotas');
  };

  return (
    <div className="mb-12">
      <h2 className="text-2xl font-bold mb-6 text-gray-800 border-b pb-2">Estrutura Recomendada</h2>

      {/* Cabeçalho da Estrutura Recomendada */}
      <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500 mb-8">
        <div className="flex flex-col md:flex-row md:justify-between md:items-center">
          <div>
            <h3 className="text-xl font-bold text-gray-800 mb-2">
              {formatStructureName(bestResult.structure)}
            </h3>
            {bestResult.variant && (
              <p className="text-md text-gray-600">{bestResult.variant}</p>
            )}
          </div>
          <div className="mt-4 md:mt-0 bg-green-50 p-3 rounded-lg">
            <p className="text-sm font-medium text-gray-600">Lucro Líquido da Empresa</p>
            <p className="text-2xl font-bold text-green-600">{formatCurrency(bestResult.netProfit)}</p>
          </div>
        </div>
      </div>

      {/* Detalhes da Estrutura Recomendada - Organizados por categorias lógicas */}
      <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4">
          <h3 className="text-lg font-bold">Detalhes Financeiros</h3>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 gap-6">
            {/* Seção: Rendimentos e Custos */}
            <div>
              <h4 className="text-md font-semibold text-blue-700 mb-3 border-b border-blue-100 pb-1">Rendimentos e Custos</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500 mb-1">Faturação Anual</p>
                  <p className="text-xl font-bold text-gray-800">
                    {formatCurrency(bestResult.taxableProfit + (bestResult.annualCosts || 0))}
                  </p>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500 mb-1">Custos Dedutíveis</p>
                  <p className="text-xl font-bold text-gray-800">
                    {formatCurrency(bestResult.annualCosts || 0)}
                  </p>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500 mb-1">Lucro Tributável</p>
                  <p className="text-xl font-bold text-gray-800">
                    {formatCurrency(bestResult.taxableProfit)}
                  </p>
                </div>
              </div>
            </div>

            {/* Seção: Impostos e Contribuições */}
            <div>
              <h4 className="text-md font-semibold text-blue-700 mb-3 border-b border-blue-100 pb-1">Impostos e Contribuições</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500 mb-1">Imposto sobre Rendimento</p>
                  <p className="text-xl font-bold text-red-600">
                    {formatCurrency(bestResult.incomeTax)}
                  </p>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500 mb-1">Segurança Social</p>
                  <p className="text-xl font-bold text-red-600">
                    {formatCurrency(bestResult.socialSecurity)}
                  </p>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500 mb-1">IVA a Pagar</p>
                  <p className="text-xl font-bold text-red-600">
                    {formatCurrency(Math.max(0, bestResult.vatPaid - bestResult.vatDeductible))}
                  </p>
                </div>
              </div>
            </div>

            {/* Seção: Resultados Finais */}
            <div>
              <h4 className="text-md font-semibold text-blue-700 mb-3 border-b border-blue-100 pb-1">Resultados Finais</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500 mb-1">Total de Impostos</p>
                  <p className="text-xl font-bold text-red-600">
                    {formatCurrency(bestResult.totalTax)}
                  </p>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500 mb-1">Taxa Efetiva de Imposto</p>
                  <p className="text-xl font-bold text-blue-600">
                    {formatPercent(bestResult.effectiveTaxRate || 0)}
                  </p>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500 mb-1">Lucro Líquido da Empresa</p>
                  <p className="text-xl font-bold text-green-600">
                    {formatCurrency(bestResult.netProfit)}
                  </p>
                </div>
                {/* Rendimento Líquido do Empresário - Removido */}
              </div>
            </div>

            {/* Seção: Detalhes de Remuneração (se aplicável) */}
            {bestResult.grossSalary && (
              <div>
                <h4 className="text-md font-semibold text-blue-700 mb-3 border-b border-blue-100 pb-1">Detalhes de Remuneração</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-500 mb-1">Salário Bruto Anual</p>
                    <p className="text-xl font-bold text-gray-800">
                      {formatCurrency(bestResult.grossSalary)}
                    </p>
                  </div>
                  {bestResult.netSalary && (
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <p className="text-sm text-gray-500 mb-1">Salário Líquido Anual</p>
                      <p className="text-xl font-bold text-blue-600">
                        {formatCurrency(bestResult.netSalary)}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Economia Fiscal */}
      <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-purple-500">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">Potencial de Economia</h3>
        <div className="flex flex-col md:flex-row md:justify-between md:items-center">
          <p className="text-gray-600">
            Comparado com a estrutura menos vantajosa ({formatStructureName(worstResult.structure)}),
            esta opção proporciona uma economia de:
          </p>
          <p className="text-2xl font-bold text-purple-600 mt-2 md:mt-0">
            {formatCurrency(taxSaving)}
          </p>
        </div>
      </div>
    </div>
  );
};

export default FiscalDashboard;
