
// Interface para as taxas de IVA
export interface VATRate {
  type: 'reduced' | 'intermediate' | 'normal';
  rate: number;
}

// Escalões de IRS para 2024
export const IRS_BRACKETS_2024 = [
  { min: 0, max: 7703, rate: 0.13, deduction: 0 },
  { min: 7703, max: 11623, rate: 0.21, deduction: 616.24 },
  { min: 11623, max: 16472, rate: 0.26, deduction: 1197.39 },
  { min: 16472, max: 21321, rate: 0.28, deduction: 1527.39 },
  { min: 21321, max: 27146, rate: 0.33, deduction: 2595.44 },
  { min: 27146, max: 39791, rate: 0.35, deduction: 3138.32 },
  { min: 39791, max: 51997, rate: 0.43, deduction: 6325.96 },
  { min: 51997, max: 81199, rate: 0.45, deduction: 7367.90 },
  { min: 81199, max: Infinity, rate: 0.48, deduction: 9799.87 }
];

// Mantendo os escalões de 2023 para compatibilidade
export const IRS_BRACKETS_2023 = [
  { min: 0, max: 7479, rate: 0.145, deduction: 0 },
  { min: 7479, max: 11284, rate: 0.21, deduction: 486.14 },
  { min: 11284, max: 15992, rate: 0.265, deduction: 1108.24 },
  { min: 15992, max: 25075, rate: 0.285, deduction: 1428.24 },
  { min: 25075, max: 36967, rate: 0.35, deduction: 3071.89 },
  { min: 36967, max: 50777, rate: 0.37, deduction: 3812.87 },
  { min: 50777, max: 78834, rate: 0.435, deduction: 7079.87 },
  { min: 78834, max: Infinity, rate: 0.48, deduction: 10602.69 }
];

// Usar os escalões de 2024 como padrão
export const IRS_BRACKETS = IRS_BRACKETS_2024;

// Limiar de isenção de IVA para 2024
export const VAT_EXEMPTION_THRESHOLD = 14500;

export const VAT_RATES: VATRate[] = [
  { type: 'reduced', rate: 0.06 },
  { type: 'intermediate', rate: 0.13 },
  { type: 'normal', rate: 0.23 }
];

// Valor do IAS (Indexante dos Apoios Sociais) para 2024
export const IAS = 522.50;

// Coeficientes do regime simplificado
export const SERVICE_COEFFICIENT = 0.70; // Prestação de serviços
export const SALES_COEFFICIENT = 0.20; // Vendas de mercadorias e hotelaria/restauração

// Taxas de IRC
export const IRC_RATE = 0.21; // Taxa geral
export const IRC_RATE_SME = 0.17; // Taxa para PMEs (até 50.000€)
export const IRC_SME_THRESHOLD = 50000; // Limite para aplicação da taxa reduzida

// Taxas de Segurança Social atualizadas para 2024
export const SS_RATES = {
  independent: 0.2375,
  independent_reduced: 0.214,
  independent_entrepreneur: 0.252, // Empresário em nome individual
  company: 0.2375,
  employee: 0.11
};
