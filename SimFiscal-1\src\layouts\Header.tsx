
import React, { useState } from 'react';
import { Link, NavLink } from 'react-router-dom';

export function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <header className="bg-gradient-to-r from-blue-800 to-blue-900 text-white shadow-lg sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          {/* Logo and brand */}
          <Link to="/" className="flex items-center space-x-3">
            <div className="bg-white text-blue-700 p-2 rounded-lg shadow-md">
              <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <div className="flex flex-col">
              <span className="text-2xl font-bold text-white">
                SimFiscal
              </span>
              <span className="text-xs text-white" style={{ color: '#FFFFFF !important' }}>Simulador Fiscal Inteligente</span>
            </div>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <NavLink
              to="/simulation"
              className={({isActive}) =>
                `flex items-center space-x-2 py-2 px-3 rounded-lg transition-colors duration-200 ${isActive ? 'bg-blue-800 text-white shadow-md' : 'text-blue-100 hover:bg-blue-800/50'}`
              }
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              <span>Simulação</span>
            </NavLink>
            <NavLink
              to="/results"
              className={({isActive}) =>
                `flex items-center space-x-2 py-2 px-3 rounded-lg transition-colors duration-200 ${isActive ? 'bg-blue-800 text-white shadow-md' : 'text-blue-100 hover:bg-blue-800/50'}`
              }
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <span>Resultados</span>
            </NavLink>
            <NavLink
              to="/fiscal-info"
              className={({isActive}) =>
                `flex items-center space-x-2 py-2 px-3 rounded-lg transition-colors duration-200 ${isActive ? 'bg-blue-800 text-white shadow-md' : 'text-blue-100 hover:bg-blue-800/50'}`
              }
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Fiscal 2025</span>
            </NavLink>
          </nav>

          {/* Contact button */}
          <div className="hidden md:block">
            <button className="bg-white text-blue-800 hover:bg-blue-50 px-4 py-2 rounded-lg shadow-md transition-all duration-200 flex items-center font-medium hover:-translate-y-0.5">
              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Contacto
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={toggleMobileMenu}
              className="p-2 rounded-md text-white hover:bg-blue-800 transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden mt-4 pb-2 border-t border-blue-600">
            <nav className="flex flex-col space-y-2 mt-4">
              <NavLink
                to="/simulation"
                onClick={() => setMobileMenuOpen(false)}
                className={({isActive}) =>
                  `flex items-center space-x-2 py-3 px-4 rounded-lg ${isActive ? 'bg-blue-800 text-white' : 'text-blue-100 hover:bg-blue-800/50'}`
                }
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                <span>Simulação</span>
              </NavLink>
              <NavLink
                to="/results"
                onClick={() => setMobileMenuOpen(false)}
                className={({isActive}) =>
                  `flex items-center space-x-2 py-3 px-4 rounded-lg ${isActive ? 'bg-blue-800 text-white' : 'text-blue-100 hover:bg-blue-800/50'}`
                }
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <span>Resultados</span>
              </NavLink>
              <NavLink
                to="/fiscal-info"
                onClick={() => setMobileMenuOpen(false)}
                className={({isActive}) =>
                  `flex items-center space-x-2 py-3 px-4 rounded-lg ${isActive ? 'bg-blue-800 text-white' : 'text-blue-100 hover:bg-blue-800/50'}`
                }
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Fiscal 2025</span>
              </NavLink>
              <button className="flex items-center space-x-2 py-3 px-4 rounded-lg bg-white text-blue-800 font-medium shadow-md">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span>Contacto</span>
              </button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
