// src/pages/FiscalInfoPage.tsx
import React from 'react';
import FiscalInfo from '../components/FiscalInfo';
import { generateFiscalPDF } from '../utils/generatePDF';

const FiscalInfoPage: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6 text-center text-blue-900">
        Informações Fiscais 2025
      </h1>
      <p className="text-lg mb-8 text-center text-gray-700">
        Consulte os parâmetros fiscais atualizados para 2025 utilizados nas simulações do SimFiscal.
      </p>

      <FiscalInfo />

      <div className="mt-8 text-center">
        <button
          onClick={generateFiscalPDF}
          className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors mr-4"
        >
          Exportar para PDF
        </button>
        <a
          href="/docs/FISCAL_2025.md"
          target="_blank"
          rel="noopener noreferrer"
          className="inline-block bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition-colors"
        >
          Ver Documentação
        </a>
      </div>
    </div>
  );
};

export default FiscalInfoPage;
