
import { DetailedCosts } from './DetailedCosts';

export type ActivityType =
  | 'services'                // Prestação de Serviços
  | 'commerce'                // Vendas de Mercadorias e Produtos
  | 'industry'                // Industrial
  | 'localAccommodation'      // Alojamento local (moradia e apartamento)
  | 'foodAndHospitality';     // Restauração, bebidas e hotelaria

export type VatRegime = 'normalMonthly' | 'normalQuarterly' | 'exempt53';

export type VatType = 'normal' | 'intermediate' | 'reduced';

export type LegalStructure =
  | 'ENI_Simplified'
  | 'ENI_Organized'
  | 'Unipessoal'
  | 'SociedadePorQuotas';

export interface FiscalParams {
  activityType: ActivityType;
  vatRegime: VatRegime;
  vatType: VatType;
  legalStructures: LegalStructure[];
  salaryMonthly?: number;
  salaryDistribution?: 'optimal' | 'custom'; // Estratégia de distribuição salarial
  salaryRatio?: number; // Percentagem do lucro como salário (0-1)
  hasOtherSocialSecurity: boolean;
  isFirstYear: boolean;
  annualRevenue: number;
  annualCosts: number; // Total de custos anuais (soma dos custos detalhados)
  detailedCosts?: DetailedCosts; // Custos detalhados por categoria
  vatThreshold?: number;
  serviceCoefficient?: number;
  withholdingTaxRate?: number; // Taxa de retenção na fonte (%) - 23% padrão, 25% opcional, 11.5% atividades não listadas
}
