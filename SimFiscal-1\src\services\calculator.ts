// src/services/calculator.ts

import type { FiscalParams, SimulationResult } from '@types';
import {
  IRS_BRACKETS,
  VAT_RATES,
  SS_RATES,
  VAT_EXEMPTION_THRESHOLD,
  VAT_IMMEDIATE_TRANSITION_THRESHOLD,
  SERVICE_COEFFICIENT,
  SALES_COEFFICIENT,
  LOCAL_ACCOMMODATION_COEFFICIENT,
  FOOD_HOSPITALITY_COEFFICIENT,
  SS_SERVICE_COEFFICIENT,
  SS_SALES_COEFFICIENT,
  SS_FOOD_HOSPITALITY_COEFFICIENT,
  IRC_RATE,
  IRC_RATE_SME,
  IRC_SME_THRESHOLD,
  IAS,
  SOLIDARITY_TAX_RATES,
  WITHHOLDING_TAX_RATES,
  SS_MINIMUM_PARTNER_CONTRIBUTION
} from '@types/TaxBrackets2025';
import {
  findOptimalSalary,
  generateSalaryDistributionSimulations,
  calculateUnipessoalWithSalary,
  calculateSociedadePorQuotasWithSalaries
} from './salaryOptimizer';

function calculateIRS(annualIncome: number): number {
  const bracket = IRS_BRACKETS.find(b => annualIncome >= b.min && annualIncome <= b.max);
  if (!bracket) return 0;

  // Cálculo do IRS base
  const baseIRS = (annualIncome * bracket.rate) - bracket.deduction;

  // Cálculo da Taxa Adicional de Solidariedade, se aplicável
  const solidarityTax = calculateSolidarityTax(annualIncome);

  return baseIRS + solidarityTax;
}

function calculateSolidarityTax(annualIncome: number): number {
  let solidarityTax = 0;

  // Aplicar a Taxa Adicional de Solidariedade para rendimentos elevados
  for (const bracket of SOLIDARITY_TAX_RATES) {
    if (annualIncome > bracket.min) {
      const taxableAmount = Math.min(annualIncome, bracket.max) - bracket.min;
      solidarityTax += taxableAmount * bracket.rate;
    }
  }

  return solidarityTax;
}

function calculateSSIndependente(annualIncome: number, hasOtherSocialSecurity: boolean, activityType: string = 'services'): number {
  // Se já tem outra SS, não paga
  if (hasOtherSocialSecurity) {
    return 0;
  }

  // Determinar coeficiente baseado no tipo de atividade (para Segurança Social)
  let ssCoefficient = SS_SERVICE_COEFFICIENT; // 70% por defeito para serviços

  if (activityType === 'sales' || activityType === 'production') {
    ssCoefficient = SS_SALES_COEFFICIENT; // 20% para produção/venda de bens
  } else if (activityType === 'hospitality' || activityType === 'food') {
    ssCoefficient = SS_FOOD_HOSPITALITY_COEFFICIENT; // 20% para hotelaria/restauração
  }

  // Rendimento relevante anual (coeficiente específico para SS)
  const relevantIncome = annualIncome * ssCoefficient;

  // Base de incidência contributiva = 1/3 do rendimento relevante trimestral
  // Para cálculo anual: relevantIncome / 3
  const annualBaseIncidence = relevantIncome / 3;
  const monthlyBaseIncidence = annualBaseIncidence / 12;

  // Limites mensais
  const minBase = 1.5 * IAS; // 783,75€ (mínimo)
  const maxBase = 12 * IAS;  // 6.270,00€ (máximo)

  // Aplicar limites
  const limitedMonthlyBase = Math.max(minBase, Math.min(maxBase, monthlyBaseIncidence));

  // Taxa de 21,4% para trabalhadores independentes
  const rate = SS_RATES.independent;

  // Contribuição mensal (mínimo 20€)
  const monthlyContribution = Math.max(20, limitedMonthlyBase * rate);

  return monthlyContribution * 12; // Total anual
}

// Função para calcular retenção na fonte para trabalhadores independentes
function calculateWithholdingTax(taxableProfit: number, withholdingRate?: number): number {
  if (!withholdingRate || withholdingRate === 0) {
    return 0;
  }

  // A retenção na fonte é aplicada sobre o lucro tributável
  return taxableProfit * (withholdingRate / 100);
}

// Função para calcular Segurança Social mínima para sócios sem salário
function calculateMinimumPartnerSS(hasOtherSocialSecurity: boolean, hasSalary: boolean): number {
  // Se já tem outra SS ou tem salário, não paga o mínimo
  if (hasOtherSocialSecurity || hasSalary) {
    return 0;
  }

  // Sócios sem salário e sem outra SS pagam sobre 1 IAS
  const monthlyBase = SS_MINIMUM_PARTNER_CONTRIBUTION;
  const rate = SS_RATES.independent; // 21,4%
  const monthlyContribution = Math.max(20, monthlyBase * rate);

  return monthlyContribution * 12; // Total anual
}

export function calculateAll(params: FiscalParams): SimulationResult[] {
  const results: SimulationResult[] = [];

  if (params.legalStructures.includes('ENI_Simplified')) {
    results.push(calculateEniSimplified(params));
  }
  if (params.legalStructures.includes('ENI_Organized')) {
    results.push(calculateEniOrganized(params));
  }
  if (params.legalStructures.includes('Unipessoal')) {
    // Unipessoal pode retornar múltiplos resultados com otimização salarial
    results.push(...calculateUnipessoal(params));
  }
  if (params.legalStructures.includes('SociedadePorQuotas')) {
    // Sociedade por Quotas pode retornar múltiplos resultados com otimização salarial
    results.push(...calculateSociedadePorQuotas(params));
  }

  // Identificar o melhor resultado global
  if (results.length > 0) {
    const bestResult = results.reduce((best, current) =>
      current.netProfit > best.netProfit ? current : best, results[0]);

    // Marcar o melhor resultado global
    results.forEach(result => {
      if (result === bestResult) {
        result.recommended = true;
        if (!result.variant) {
          result.variant = 'Recomendado';
        } else if (!result.variant.includes('Recomendado')) {
          result.variant = `Recomendado: ${result.variant}`;
        }
      }
    });
  }

  // Calcular a taxa efetiva de imposto para todos os resultados
  results.forEach(result => {
    if (!result.effectiveTaxRate && result.taxableProfit > 0) {
      result.effectiveTaxRate = (result.totalTax / result.taxableProfit) * 100;
    }
  });

  return results;
}

function calculateEniSimplified(params: FiscalParams): SimulationResult {
  // Usar o coeficiente correto com base no tipo de atividade
  let coef;
  switch (params.activityType) {
    case 'services':
      coef = SERVICE_COEFFICIENT;
      break;
    case 'commerce':
      coef = SALES_COEFFICIENT;
      break;
    case 'industry':
      coef = SALES_COEFFICIENT; // Usa o mesmo coeficiente das vendas
      break;
    case 'localAccommodation':
      coef = LOCAL_ACCOMMODATION_COEFFICIENT;
      break;
    case 'foodAndHospitality':
      coef = FOOD_HOSPITALITY_COEFFICIENT;
      break;
    default:
      coef = SERVICE_COEFFICIENT; // Valor padrão
  }

  // Faturação anual (rendimento bruto)
  const annualRevenue = params.annualRevenue;

  // Custos dedutíveis
  const annualCosts = params.annualCosts;

  // Cálculo do rendimento relevante (aplicação do coeficiente ao rendimento bruto)
  // No regime simplificado, os custos já estão considerados através do coeficiente
  const relevantIncome = annualRevenue * coef;
  const taxableProfit = relevantIncome;

  // Cálculo do IRS
  const incomeTax = calculateIRS(taxableProfit);
  const solidarityTax = calculateSolidarityTax(taxableProfit);

  // Cálculo da Segurança Social
  const socialSecurity = calculateSSIndependente(taxableProfit, params.hasOtherSocialSecurity, params.activityType);

  // Cálculo da Retenção na Fonte
  const withholdingTax = calculateWithholdingTax(taxableProfit, params.withholdingTaxRate);

  // Cálculo do IVA
  const vatRate = VAT_RATES.find(v => v.type === params.vatType)?.rate || 0.23;
  const vatThreshold = VAT_EXEMPTION_THRESHOLD;
  const vatImmediateThreshold = VAT_IMMEDIATE_TRANSITION_THRESHOLD;

  let vatPaid = 0;
  let vatDeductible = 0;

  if (params.vatRegime === 'exempt53') {
    // Regras de 2025 para o regime de isenção do artigo 53º do CIVA
    if (annualRevenue <= vatThreshold) {
      // Abaixo do limiar de €15.000: isenção total
      vatPaid = 0;
      vatDeductible = 0;
    } else if (annualRevenue <= vatImmediateThreshold) {
      // Entre €15.000 e €18.750: mantém isenção até final do ano
      // O IVA só será devido a partir de 1 de janeiro do ano seguinte
      vatPaid = 0;
      vatDeductible = 0;
    } else {
      // Acima de €18.750 (125% do limiar): transição imediata para regime normal
      // A fatura em que o volume de negócios de €18.750 é excedido já tem IVA
      const taxableRevenue = annualRevenue - vatImmediateThreshold;
      vatPaid = taxableRevenue * vatRate;
      vatDeductible = annualCosts * vatRate * (taxableRevenue / annualRevenue); // Proporção dedutível
    }
  } else {
    // Regime normal de IVA
    vatPaid = annualRevenue * vatRate;
    vatDeductible = annualCosts * vatRate;
  }

  const vatBalance = Math.max(0, vatPaid - vatDeductible);

  // Cálculo da carga fiscal total
  const totalTax = incomeTax + solidarityTax + socialSecurity + vatBalance;

  // Cálculo do lucro líquido
  // No regime simplificado, os custos reais não são deduzidos para efeitos fiscais,
  // mas afetam o lucro líquido real
  // Garantir que todos os custos são deduzidos corretamente
  // Nota: ENI não tem salários formais, pois o empresário não é funcionário da própria empresa
  const netProfit = annualRevenue - annualCosts - totalTax;

  // Taxa efetiva de imposto
  const effectiveTaxRate = taxableProfit > 0 ? (totalTax / taxableProfit) * 100 : 0;

  return {
    structure: 'ENI_Simplified',
    taxableProfit,
    incomeTax,
    solidarityTax,
    socialSecurity,
    withholdingTax,
    vatPaid,
    vatDeductible,
    totalTax,
    netProfit,
    annualCosts: annualCosts,
    vatThreshold: vatThreshold,
    serviceCoefficient: coef,
    effectiveTaxRate,
  };
}

function calculateEniOrganized(params: FiscalParams): SimulationResult {
  // Faturação anual (rendimento bruto)
  const annualRevenue = params.annualRevenue;

  // Custos dedutíveis
  const annualCosts = params.annualCosts;

  // Na contabilidade organizada, o lucro tributável é a diferença entre rendimentos e gastos
  const taxableProfit = annualRevenue - annualCosts;

  // Cálculo do IRS
  const incomeTax = calculateIRS(taxableProfit);
  const solidarityTax = calculateSolidarityTax(taxableProfit);

  // Cálculo da Segurança Social
  // Na contabilidade organizada, a SS é calculada sobre o lucro tributável
  const socialSecurity = calculateSSIndependente(taxableProfit, params.hasOtherSocialSecurity, params.activityType);

  // Cálculo da Retenção na Fonte
  const withholdingTax = calculateWithholdingTax(taxableProfit, params.withholdingTaxRate);

  // Cálculo do IVA
  const vatRate = VAT_RATES.find(v => v.type === params.vatType)?.rate || 0.23;
  const vatThreshold = VAT_EXEMPTION_THRESHOLD;
  const vatImmediateThreshold = VAT_IMMEDIATE_TRANSITION_THRESHOLD;

  let vatPaid = 0;
  let vatDeductible = 0;

  if (params.vatRegime === 'exempt53') {
    // Regras de 2025 para o regime de isenção do artigo 53º do CIVA
    if (annualRevenue <= vatThreshold) {
      // Abaixo do limiar de €15.000: isenção total
      vatPaid = 0;
      vatDeductible = 0;
    } else if (annualRevenue <= vatImmediateThreshold) {
      // Entre €15.000 e €18.750: mantém isenção até final do ano
      // O IVA só será devido a partir de 1 de janeiro do ano seguinte
      vatPaid = 0;
      vatDeductible = 0;
    } else {
      // Acima de €18.750 (125% do limiar): transição imediata para regime normal
      // A fatura em que o volume de negócios de €18.750 é excedido já tem IVA
      const taxableRevenue = annualRevenue - vatImmediateThreshold;
      vatPaid = taxableRevenue * vatRate;
      vatDeductible = annualCosts * vatRate * (taxableRevenue / annualRevenue); // Proporção dedutível
    }
  } else {
    // Regime normal de IVA
    vatPaid = annualRevenue * vatRate;
    vatDeductible = annualCosts * vatRate;
  }

  const vatBalance = Math.max(0, vatPaid - vatDeductible);

  // Cálculo da carga fiscal total
  const totalTax = incomeTax + solidarityTax + socialSecurity + vatBalance;

  // Cálculo do lucro líquido
  // Nota: ENI não tem salários formais, pois o empresário não é funcionário da própria empresa
  const netProfit = annualRevenue - annualCosts - totalTax;

  // Taxa efetiva de imposto
  const effectiveTaxRate = taxableProfit > 0 ? (totalTax / taxableProfit) * 100 : 0;

  return {
    structure: 'ENI_Organized',
    taxableProfit,
    incomeTax,
    solidarityTax,
    socialSecurity,
    withholdingTax,
    vatPaid,
    vatDeductible,
    totalTax,
    netProfit,
    annualCosts: annualCosts,
    vatThreshold: vatThreshold,
    serviceCoefficient: 1.0, // Contabilidade organizada usa 100% dos custos
    effectiveTaxRate,

  };
}

function calculateUnipessoal(params: FiscalParams): SimulationResult[] {
  const annualRevenue = params.annualRevenue;
  const annualCosts = params.annualCosts;
  const vatRate = VAT_RATES.find(v => v.type === params.vatType)?.rate || 0.23;
  const vatThreshold = VAT_EXEMPTION_THRESHOLD;
  const vatImmediateThreshold = VAT_IMMEDIATE_TRANSITION_THRESHOLD;

  // Usar o método tradicional com salário fixo ou sem salário
  const monthlySalary = params.salaryMonthly || 0;

  // Importante: O cálculo do lucro líquido para Sociedade Unipessoal já inclui a dedução dos salários
  // na função calculateUnipessoalWithSalary, onde:
  // netProfit = annualRevenue - operationalCosts - totalSalaryCost - corporateTax - dividendTax
  const result = calculateUnipessoalWithSalary(params, monthlySalary);

  // Calcular IVA
  let vatPaid = 0;
  let vatDeductible = 0;

  if (params.vatRegime === 'exempt53') {
    // Regras de 2025 para o regime de isenção do artigo 53º do CIVA
    // Nota: A partir de 2025, sociedades também podem beneficiar do regime de isenção
    if (annualRevenue <= vatThreshold) {
      // Abaixo do limiar de €15.000: isenção total
      vatPaid = 0;
      vatDeductible = 0;
    } else if (annualRevenue <= vatImmediateThreshold) {
      // Entre €15.000 e €18.750: mantém isenção até final do ano
      // O IVA só será devido a partir de 1 de janeiro do ano seguinte
      vatPaid = 0;
      vatDeductible = 0;
    } else {
      // Acima de €18.750 (125% do limiar): transição imediata para regime normal
      // A fatura em que o volume de negócios de €18.750 é excedido já tem IVA
      const taxableRevenue = annualRevenue - vatImmediateThreshold;
      vatPaid = taxableRevenue * vatRate;
      vatDeductible = annualCosts * vatRate * (taxableRevenue / annualRevenue); // Proporção dedutível
    }
  } else {
    // Regime normal de IVA
    vatPaid = annualRevenue * vatRate;
    vatDeductible = annualCosts * vatRate;
  }

  const vatBalance = Math.max(0, vatPaid - vatDeductible);

  // Atualizar resultado com IVA
  result.vatPaid = vatPaid;
  result.vatDeductible = vatDeductible;
  result.totalTax += vatBalance;
  result.netProfit -= vatBalance;
  result.vatThreshold = vatThreshold;

  // Garantir que a taxa efetiva seja calculada corretamente
  if (!result.effectiveTaxRate && result.taxableProfit > 0) {
    result.effectiveTaxRate = (result.totalTax / result.taxableProfit) * 100;
  }

  return [result];
}

function calculateSociedadePorQuotas(params: FiscalParams): SimulationResult[] {
  const results: SimulationResult[] = [];
  const annualRevenue = params.annualRevenue;
  const annualCosts = params.annualCosts;
  const vatRate = VAT_RATES.find(v => v.type === params.vatType)?.rate || 0.23;
  const vatThreshold = VAT_EXEMPTION_THRESHOLD;
  const vatImmediateThreshold = VAT_IMMEDIATE_TRANSITION_THRESHOLD;

  // Calcular o salário mensal base com base no lucro tributável
  const baseMonthlyIncome = Math.max(870, Math.min(3000, Math.round((params.annualRevenue - params.annualCosts) / 14 / 1.5)));

  // Simular com diferentes configurações de sócios
  const scenarios = [
    {
      label: '1 sócio-gerente',
      salaries: [baseMonthlyIncome]
    },
    {
      label: '2 sócios-gerentes (salários iguais)',
      salaries: [Math.round(baseMonthlyIncome * 0.7), Math.round(baseMonthlyIncome * 0.7)]
    },
    {
      label: '1 sócio-gerente + 1 sócio não gerente',
      salaries: [Math.round(baseMonthlyIncome * 1.2), 0]
    }
  ];

  // Importante: O cálculo do lucro líquido para Sociedade por Quotas já inclui a dedução dos salários
  // na função calculateSociedadePorQuotasWithSalaries, onde:
  // netProfit = annualRevenue - operationalCosts - totalSalaryCost - corporateTax - dividendTax
  for (const scenario of scenarios) {
    const result = calculateSociedadePorQuotasWithSalaries(params, scenario.salaries);
    result.variant = scenario.label;

    // Calcular IVA
    let vatPaid = 0;
    let vatDeductible = 0;

    if (params.vatRegime === 'exempt53') {
      // Regras de 2025 para o regime de isenção do artigo 53º do CIVA
      // Nota: A partir de 2025, sociedades também podem beneficiar do regime de isenção
      if (annualRevenue <= vatThreshold) {
        // Abaixo do limiar de €15.000: isenção total
        vatPaid = 0;
        vatDeductible = 0;
      } else if (annualRevenue <= vatImmediateThreshold) {
        // Entre €15.000 e €18.750: mantém isenção até final do ano
        // O IVA só será devido a partir de 1 de janeiro do ano seguinte
        vatPaid = 0;
        vatDeductible = 0;
      } else {
        // Acima de €18.750 (125% do limiar): transição imediata para regime normal
        // A fatura em que o volume de negócios de €18.750 é excedido já tem IVA
        const taxableRevenue = annualRevenue - vatImmediateThreshold;
        vatPaid = taxableRevenue * vatRate;
        vatDeductible = annualCosts * vatRate * (taxableRevenue / annualRevenue); // Proporção dedutível
      }
    } else {
      // Regime normal de IVA
      vatPaid = annualRevenue * vatRate;
      vatDeductible = annualCosts * vatRate;
    }

    const vatBalance = Math.max(0, vatPaid - vatDeductible);

    // Atualizar resultado com IVA
    result.vatPaid = vatPaid;
    result.vatDeductible = vatDeductible;
    result.totalTax += vatBalance;
    result.netProfit -= vatBalance;
    result.vatThreshold = vatThreshold;

    // Garantir que a taxa efetiva seja calculada corretamente
    if (!result.effectiveTaxRate && result.taxableProfit > 0) {
      result.effectiveTaxRate = (result.totalTax / result.taxableProfit) * 100;
    }

    results.push(result);
  }

  // Identificar o melhor resultado
  const bestResult = results.reduce((best, current) =>
    current.netProfit > best.netProfit ? current : best, results[0]);

  if (bestResult) {
    bestResult.recommended = true;
    bestResult.variant = `Recomendado: ${bestResult.variant}`;
  }

  return results;
}