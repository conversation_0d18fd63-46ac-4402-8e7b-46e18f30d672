// src/components/FiscalRecommendations.tsx
import React from 'react';
import { FiscalParams, SimulationResult } from '@types';
import {
  VAT_EXEMPTION_THRESHOLD,
  IAS,
  IRC_SME_THRESHOLD
} from '@types/TaxBrackets2025';
import { formatCurrency } from '../utils/formatters';

interface FiscalRecommendation {
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  applicableTo: string[];
}

interface FiscalRecommendationsProps {
  params: FiscalParams;
  results: SimulationResult[];
}

const FiscalRecommendations: React.FC<FiscalRecommendationsProps> = ({ params, results }) => {
  if (!results || results.length === 0) return null;

  // Encontrar a estrutura mais vantajosa
  const bestResult = results.reduce(
    (prev, current) => (prev.netProfit > current.netProfit) ? prev : current
  );

  // Gerar recomendações com base nos parâmetros e resultados
  const recommendations: FiscalRecommendation[] = [];

  // Verificar limiar de IVA
  if (params.annualRevenue < VAT_EXEMPTION_THRESHOLD * 1.1 &&
      params.annualRevenue > VAT_EXEMPTION_THRESHOLD * 0.9) {
    recommendations.push({
      title: "Atenção ao Limiar de Isenção de IVA",
      description: `O seu volume de negócios está próximo do limiar de isenção de IVA (${formatCurrency(VAT_EXEMPTION_THRESHOLD)}). Considere planear as suas vendas para otimizar a situação fiscal.`,
      impact: "high",
      applicableTo: ["ENI_Simplified", "ENI_Organized"]
    });
  }

  // Verificar otimização salarial
  const unipessoalResults = results.filter(r => r.structure === "Unipessoal");
  if (unipessoalResults.length > 0) {
    const bestUnipessoal = unipessoalResults.reduce(
      (prev, current) => (prev.netProfit > current.netProfit) ? prev : current
    );

    if (bestUnipessoal.grossSalary) {
      recommendations.push({
        title: "Otimização Salarial",
        description: "Considere ajustar o salário para otimizar a carga fiscal global, equilibrando os custos com Segurança Social e IRS.",
        impact: "medium",
        applicableTo: ["Unipessoal", "SociedadePorQuotas"]
      });
    }
  }

  // Verificar benefícios fiscais potenciais
  if (params.annualCosts > 50000) {
    recommendations.push({
      title: "Potencial para Benefícios Fiscais",
      description: "O nível de despesas sugere potencial para benefícios fiscais como SIFIDE ou RFAI. Consulte um especialista fiscal.",
      impact: "medium",
      applicableTo: ["Unipessoal", "SociedadePorQuotas"]
    });
  }

  // Verificar limite de IRC para PMEs
  if (bestResult.structure === "Unipessoal" || bestResult.structure === "SociedadePorQuotas") {
    if (bestResult.taxableProfit > IRC_SME_THRESHOLD * 0.9 && bestResult.taxableProfit < IRC_SME_THRESHOLD * 1.1) {
      recommendations.push({
        title: "Atenção ao Limite de IRC para PMEs",
        description: `O seu lucro tributável está próximo do limite para a taxa reduzida de IRC (${formatCurrency(IRC_SME_THRESHOLD)}). Considere planear os seus gastos para otimizar a situação fiscal.`,
        impact: "high",
        applicableTo: ["Unipessoal", "SociedadePorQuotas"]
      });
    }
  }

  // Verificar limite de Segurança Social
  if (bestResult.structure === "ENI_Simplified" || bestResult.structure === "ENI_Organized") {
    const relevantIncome = bestResult.taxableProfit;
    const monthlyBaseIncidence = relevantIncome / 12;
    const maxBase = 12 * IAS;

    if (monthlyBaseIncidence > maxBase * 0.9) {
      recommendations.push({
        title: "Limite de Contribuições para a Segurança Social",
        description: `O seu rendimento relevante está próximo ou acima do limite máximo de incidência para a Segurança Social (${formatCurrency(maxBase)} mensais). Valores acima deste limite não aumentam as suas contribuições.`,
        impact: "medium",
        applicableTo: ["ENI_Simplified", "ENI_Organized"]
      });
    }
  }

  // Verificar se há uma grande diferença entre as estruturas
  if (results.length > 1) {
    const worstResult = results.reduce(
      (prev, current) => (prev.netProfit < current.netProfit) ? prev : current
    );

    const difference = bestResult.netProfit - worstResult.netProfit;
    const percentageDifference = difference / worstResult.netProfit * 100;

    if (percentageDifference > 20) {
      recommendations.push({
        title: "Grande Diferença entre Estruturas",
        description: `Existe uma diferença significativa (${percentageDifference.toFixed(1)}%) entre a melhor e a pior estrutura. Considere mudar para ${bestResult.structure.replace('ENI_', 'ENI ').replace('SociedadePorQuotas', 'Sociedade por Quotas')} para otimizar a sua situação fiscal.`,
        impact: "high",
        applicableTo: ["ENI_Simplified", "ENI_Organized", "Unipessoal", "SociedadePorQuotas"]
      });
    }
  }

  // Filtrar recomendações aplicáveis à estrutura recomendada
  const applicableRecommendations = recommendations.filter(
    rec => rec.applicableTo.includes(bestResult.structure)
  );

  if (applicableRecommendations.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
      <h3 className="text-xl font-semibold text-gray-800 mb-4">Recomendações Fiscais</h3>

      <div className="space-y-4">
        {applicableRecommendations.map((rec, index) => (
          <div
            key={index}
            className={`p-4 rounded-lg border ${
              rec.impact === 'high'
                ? 'bg-red-50 border-red-200'
                : rec.impact === 'medium'
                  ? 'bg-yellow-50 border-yellow-200'
                  : 'bg-blue-50 border-blue-200'
            }`}
          >
            <div className="flex items-start">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
                rec.impact === 'high'
                  ? 'bg-red-100 text-red-600'
                  : rec.impact === 'medium'
                    ? 'bg-yellow-100 text-yellow-600'
                    : 'bg-blue-100 text-blue-600'
              }`}>
                {rec.impact === 'high' ? (
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                ) : rec.impact === 'medium' ? (
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                )}
              </div>
              <div>
                <h4 className={`font-semibold ${
                  rec.impact === 'high'
                    ? 'text-red-800'
                    : rec.impact === 'medium'
                      ? 'text-yellow-800'
                      : 'text-blue-800'
                }`}>
                  {rec.title}
                </h4>
                <p className={`mt-1 text-sm ${
                  rec.impact === 'high'
                    ? 'text-red-600'
                    : rec.impact === 'medium'
                      ? 'text-yellow-600'
                      : 'text-blue-600'
                }`}>
                  {rec.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FiscalRecommendations;
