import { useState, useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useNavigate } from 'react-router-dom';
import { calculateAll } from '@services/calculator';
import { exportToPDF } from '@services/pdfExport';
import { saveSimulation } from '@services/storageService';
import { BarChart } from '../charts/BarChart';
import { PieChart } from '../charts/PieChart';
import SalaryOptimizationOptions from '../components/SalaryOptimizationOptions';
import DetailedCostsInput from '../components/DetailedCostsInput';
import FiscalDashboard from '../components/FiscalDashboard';
import FiscalRecommendations from '../components/FiscalRecommendations';
import ComparisonTable from '../components/ComparisonTable';
import VATThresholdAlert from '../components/VATThresholdAlert';
import type { FiscalParams, SimulationResult, DetailedCosts } from '../types';
import { createEmptyDetailedCosts, calculateTotalCosts } from '../types/DetailedCosts';
import { formatCurrency, formatPercent } from '../utils/formatters';

const schema = yup.object({
  name: yup.string().required('Nome da empresa é obrigatório'),
  responsible: yup.string().required('Responsável é obrigatório'),
  startDate: yup.date().required('Data de início é obrigatória'),
  annualRevenue: yup.number().min(0).required('Faturação é obrigatória'),
  sensitivity: yup.number().min(80).max(120).required(),
  annualCosts: yup.number().min(0).required('Custos são obrigatórios'),
  activityType: yup.string().required('Tipo de atividade é obrigatório'),
  vatRegime: yup.string().required('Regime de IVA é obrigatório'),
  legalStructures: yup.array().min(1, 'Escolha pelo menos um regime'),
  salaryMonthly: yup.number().min(0),
  hasOtherSocialSecurity: yup.boolean(),
  withholdingTaxRate: yup.number().min(0).max(100),
}).required();

// Removido tipo FormInputs para usar any

const Tooltip = ({ text }: { text: string }) => (
  <div className="group relative inline-block">
    <svg xmlns="http://www.w3.org/2000/svg" className="inline-block ml-1 w-5 h-5 text-gray-500 hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <div className="hidden group-hover:block absolute z-10 w-64 p-2 bg-gray-800 text-white text-sm rounded shadow-lg">
      {text}
    </div>
  </div>
);

export function SimulationPage() {
  const navigate = useNavigate();
  const [step, setStep] = useState(1);
  const [results, setResults] = useState<SimulationResult[]>([]);
  const [vatThreshold, setVatThreshold] = useState(15000);
  const [serviceCoefficient, setServiceCoefficient] = useState(0.75);
  const [detailedCosts, setDetailedCosts] = useState<DetailedCosts>(createEmptyDetailedCosts());
  const resultsRef = useRef<HTMLDivElement>(null);
  const simulationFormRef = useRef<HTMLDivElement>(null);

  // Formatador para valores monetários
  const fmt = {
    format: (value: number) => formatCurrency(value)
  };

  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm<any>({
    resolver: yupResolver(schema),
    defaultValues: {
      sensitivity: 100,
      startDate: new Date(),
      legalStructures: ['ENI_Simplified', 'Unipessoal'], // Selecionar algumas estruturas por padrão
      activityType: 'services',
      vatRegime: 'normalMonthly',
      optimizeDividends: true, // Ativar otimização por padrão
      salaryMonthly: 1000 // Salário mensal padrão
    }
  });

  // Verificar se há estruturas que podem usar otimização salarial
  const showSalaryOptions = watch('legalStructures')?.some((s: string) =>
    s === 'Unipessoal' || s === 'SociedadePorQuotas'
  );

  // Atualizar o valor de annualCosts quando os custos detalhados mudarem
  useEffect(() => {
    const totalCosts = calculateTotalCosts(detailedCosts);
    setValue('annualCosts', totalCosts);
  }, [detailedCosts, setValue]);

  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <div className="space-y-8">
            <div className="flex items-center space-x-4 mb-8">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">Dados da Empresa</h2>
                <p className="text-gray-600">Informações básicas sobre a sua empresa ou atividade</p>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="form-group md:col-span-2">
                  <label className="block text-gray-700 font-bold mb-2">
                    Nome da Empresa
                    <Tooltip text="Nome comercial ou denominação social da sua empresa" />
                  </label>
                  <input
                    {...register('name')}
                    placeholder="Ex: Empresa ABC, Lda."
                    className="form-input w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {errors.name && <p className="text-red-600 mt-1">{errors.name?.message as string}</p>}
                </div>

                <div className="form-group">
                  <label className="block text-gray-700 font-bold mb-2">
                    Responsável
                    <Tooltip text="Nome do sócio-gerente ou empresário" />
                  </label>
                  <input
                    {...register('responsible')}
                    placeholder="Ex: João Silva"
                    className="form-input w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {errors.responsible && <p className="text-red-600 mt-1">{errors.responsible?.message as string}</p>}
                </div>

                <div className="form-group">
                  <label className="block text-gray-700 font-bold mb-2">
                    Data de Início de Atividade
                    <Tooltip text="Data de início de atividade nas Finanças" />
                  </label>
                  <input
                    type="date"
                    {...register('startDate')}
                    className="form-input w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {errors.startDate && <p className="text-red-600 mt-1">{errors.startDate?.message as string}</p>}
                </div>
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
              <div className="flex items-start">
                <svg className="h-6 w-6 text-blue-500 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h4 className="font-semibold text-blue-800">Porquê esta informação?</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    Estes dados são importantes para personalizar a sua simulação e determinar se o seu negócio está no primeiro ano de atividade,
                    o que pode influenciar alguns benefícios fiscais.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-8">
            <div className="flex items-center space-x-4 mb-8">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">Parâmetros Fiscais</h2>
                <p className="text-gray-600">Informações financeiras para a simulação fiscal</p>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="form-group md:col-span-2">
                  <label className="block text-gray-700 font-bold mb-2">
                    Faturação Anual (€)
                    <Tooltip text="Valor total esperado de faturação nos próximos 12 meses" />
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500">€</span>
                    </div>
                    <input
                      type="number"
                      {...register('annualRevenue')}
                      placeholder="Ex: 50000"
                      className="form-input w-full pl-8 px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  {errors.annualRevenue && <p className="text-red-600 mt-1">{errors.annualRevenue?.message as string}</p>}
                </div>



                <div className="form-group md:col-span-2">
                  <label className="block text-gray-700 font-bold mb-2">
                    Tipo de Atividade
                    <Tooltip text="Selecione o tipo de atividade principal com o respetivo coeficiente para 2025" />
                  </label>
                  <select
                    {...register('activityType')}
                    className="form-select w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="services">Prestação de Serviços</option>
                    <option value="commerce">Vendas de Mercadorias e Produtos</option>
                    <option value="industry">Industrial</option>
                    <option value="localAccommodation">Alojamento Local (Moradia e Apartamento)</option>
                    <option value="foodAndHospitality">Restauração, Bebidas e Hotelaria</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="bg-green-50 p-4 rounded-lg border border-green-100">
              <div className="flex items-start">
                <svg className="h-6 w-6 text-green-500 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h4 className="font-semibold text-green-800">Coeficientes para 2025</h4>
                  <p className="text-sm text-green-700 mt-1">
                    Para o regime simplificado de <strong>IRS</strong>, os coeficientes aplicáveis em 2025 são:
                  </p>
                  <ul className="list-disc list-inside text-sm text-green-700 mt-1 ml-2">
                    <li><span className="font-semibold">75%</span> para prestação de serviços</li>
                    <li><span className="font-semibold">15%</span> para vendas de mercadorias e produtos</li>
                    <li><span className="font-semibold">35%</span> para rendimentos de alojamento local na modalidade de moradia e apartamento</li>
                    <li><span className="font-semibold">15%</span> para prestações de serviços de restauração e bebidas e atividades hoteleiras e similares</li>
                  </ul>
                  <p className="text-sm text-green-700 mt-2">
                    <strong>Nota:</strong> Para a <strong>Segurança Social</strong>, os coeficientes são diferentes: 70% para serviços e 20% para vendas/hotelaria.
                  </p>
                  <p className="text-sm text-green-700 mt-2 font-semibold">
                    No próximo passo, poderá detalhar todos os custos da sua atividade por categoria.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-8">
            <div className="flex items-center space-x-4 mb-8">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">Custos Detalhados</h2>
                <p className="text-gray-600">Detalhe os custos da sua atividade por categoria</p>
              </div>
            </div>

            <DetailedCostsInput
              costs={detailedCosts}
              onChange={setDetailedCosts}
            />

            <div className="bg-green-50 p-4 rounded-lg border border-green-100">
              <div className="flex items-start">
                <svg className="h-6 w-6 text-green-500 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h4 className="font-semibold text-green-800">Importância dos Custos Detalhados</h4>
                  <p className="text-sm text-green-700 mt-1">
                    O detalhe dos custos permite uma simulação fiscal mais precisa, especialmente para o cálculo do IVA dedutível.
                    Preencha com os valores estimados para os próximos 12 meses.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-8">
            <div className="flex items-center space-x-4 mb-8">
              <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">Escolha de Regimes</h2>
                <p className="text-gray-600">Selecione os regimes fiscais e estruturas jurídicas a simular</p>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
              <h3 className="text-lg font-semibold mb-4 text-gray-700">Regime de IVA</h3>
              <div className="form-group mb-6">
                <label className="block text-gray-700 font-bold mb-2">
                  Selecione o regime de IVA
                  <Tooltip text="Art.º 53.º: Isenção para volume negócios até €15.000 em 2025" />
                </label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className={`p-5 border rounded-lg cursor-pointer transition-all ${watch('vatRegime') === 'normalMonthly' ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 hover:border-blue-300'}`}>
                    <input
                      type="radio"
                      id="vatRegime_normalMonthly"
                      value="normalMonthly"
                      {...register('vatRegime')}
                      className="hidden"
                    />
                    <label htmlFor="vatRegime_normalMonthly" className="cursor-pointer flex flex-col h-full">
                      <div className="font-bold text-blue-700 text-lg">Regime Normal (Mensal)</div>
                      <div className="text-sm text-gray-600 mt-2">
                        Declaração e pagamento de IVA mensalmente. Adequado para empresas com volume de negócios mais elevado.
                      </div>
                    </label>
                  </div>

                  <div className={`p-5 border rounded-lg cursor-pointer transition-all ${watch('vatRegime') === 'normalQuarterly' ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 hover:border-blue-300'}`}>
                    <input
                      type="radio"
                      id="vatRegime_normalQuarterly"
                      value="normalQuarterly"
                      {...register('vatRegime')}
                      className="hidden"
                    />
                    <label htmlFor="vatRegime_normalQuarterly" className="cursor-pointer flex flex-col h-full">
                      <div className="font-bold text-blue-700 text-lg">Regime Normal (Trimestral)</div>
                      <div className="text-sm text-gray-600 mt-2">
                        Declaração e pagamento de IVA trimestralmente. Opção mais comum para pequenas empresas.
                      </div>
                    </label>
                  </div>

                  <div className={`p-5 border rounded-lg cursor-pointer transition-all ${watch('vatRegime') === 'exempt53' ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 hover:border-blue-300'}`}>
                    <input
                      type="radio"
                      id="vatRegime_exempt53"
                      value="exempt53"
                      {...register('vatRegime')}
                      className="hidden"
                    />
                    <label htmlFor="vatRegime_exempt53" className="cursor-pointer flex flex-col h-full">
                      <div className="font-bold text-blue-700 text-lg">Regime de Isenção (Art.º 53.º)</div>
                      <div className="text-sm text-gray-600 mt-2">
                        Isenção de IVA para empresas com faturação anual até €15.000. Não permite deduzir IVA nas compras.
                      </div>
                    </label>
                  </div>
                </div>
              </div>

              <div className="border-t border-gray-200 my-6 pt-6">
                <h3 className="text-lg font-semibold mb-4 text-gray-700">Estruturas Jurídicas</h3>
                <div className="form-group">
                  <label className="block text-gray-700 font-bold mb-2">
                    Selecione as estruturas a simular
                    <Tooltip text="Pode selecionar múltiplas opções para comparação" />
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className={`p-5 border rounded-lg cursor-pointer transition-all ${watch('legalStructures')?.includes('ENI_Simplified') ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 hover:border-blue-300'}`}>
                      <input
                        type="checkbox"
                        id="structure_ENI_Simplified"
                        value="ENI_Simplified"
                        {...register('legalStructures')}
                        className="hidden"
                        defaultChecked={true}
                      />
                      <label htmlFor="structure_ENI_Simplified" className="cursor-pointer flex flex-col h-full">
                        <div className="font-bold text-blue-700 text-lg">ENI Simplificado</div>
                        <div className="text-sm text-gray-600 mt-2">
                          Regime simplificado para empresários em nome individual. Tributação com base em coeficientes aplicados ao rendimento bruto.
                        </div>
                      </label>
                    </div>

                    <div className={`p-5 border rounded-lg cursor-pointer transition-all ${watch('legalStructures')?.includes('ENI_Organized') ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 hover:border-blue-300'}`}>
                      <input
                        type="checkbox"
                        id="structure_ENI_Organized"
                        value="ENI_Organized"
                        {...register('legalStructures')}
                        className="hidden"
                      />
                      <label htmlFor="structure_ENI_Organized" className="cursor-pointer flex flex-col h-full">
                        <div className="font-bold text-blue-700 text-lg">ENI Contabilidade Organizada</div>
                        <div className="text-sm text-gray-600 mt-2">
                          Regime de contabilidade organizada para empresários em nome individual. Tributação com base no lucro real.
                        </div>
                      </label>
                    </div>

                    <div className={`p-5 border rounded-lg cursor-pointer transition-all ${watch('legalStructures')?.includes('Unipessoal') ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 hover:border-blue-300'}`}>
                      <input
                        type="checkbox"
                        id="structure_Unipessoal"
                        value="Unipessoal"
                        {...register('legalStructures')}
                        className="hidden"
                        defaultChecked={true}
                      />
                      <label htmlFor="structure_Unipessoal" className="cursor-pointer flex flex-col h-full">
                        <div className="font-bold text-blue-700 text-lg">Sociedade Unipessoal</div>
                        <div className="text-sm text-gray-600 mt-2">
                          Sociedade por quotas com um único sócio. Permite separação entre património pessoal e empresarial.
                        </div>
                      </label>
                    </div>

                    <div className={`p-5 border rounded-lg cursor-pointer transition-all ${watch('legalStructures')?.includes('SociedadePorQuotas') ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 hover:border-blue-300'}`}>
                      <input
                        type="checkbox"
                        id="structure_SociedadePorQuotas"
                        value="SociedadePorQuotas"
                        {...register('legalStructures')}
                        className="hidden"
                      />
                      <label htmlFor="structure_SociedadePorQuotas" className="cursor-pointer flex flex-col h-full">
                        <div className="font-bold text-blue-700 text-lg">Sociedade por Quotas</div>
                        <div className="text-sm text-gray-600 mt-2">
                          Sociedade com múltiplos sócios. Permite distribuição de lucros e otimização fiscal através de diferentes configurações de sócios.
                        </div>
                      </label>
                    </div>
                  </div>
                  {errors.legalStructures && <p className="text-red-600 mt-2">{errors.legalStructures?.message as string}</p>}
                </div>
              </div>
            </div>

            {/* Opções de Otimização Salarial */}
            {showSalaryOptions && (
              <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-700">Opções de Remuneração</h3>
                <SalaryOptimizationOptions
                  params={watch()}
                  onChange={(newParams) => {
                    Object.entries(newParams).forEach(([key, value]) => {
                      if (key !== 'legalStructures') { // Evitar sobrescrever arrays
                        setValue(key, value);
                      }
                    });
                  }}
                  showOptions={showSalaryOptions}
                />
              </div>
            )}

            <div className="bg-indigo-50 p-4 rounded-lg border border-indigo-100">
              <div className="flex items-start">
                <svg className="h-6 w-6 text-indigo-500 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h4 className="font-semibold text-indigo-800">Comparação de Estruturas</h4>
                  <p className="text-sm text-indigo-700 mt-1">
                    Selecione múltiplas estruturas para obter uma análise comparativa detalhada.
                    Cada estrutura tem vantagens e desvantagens fiscais dependendo do seu volume de negócios e tipo de atividade.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-8">
            <div className="flex items-center space-x-4 mb-8">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">Revisão & Simular</h2>
                <p className="text-gray-600">Verifique os dados e execute a simulação fiscal</p>
              </div>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-md border border-gray-100">
              <h3 className="text-xl font-bold mb-6 text-blue-700 border-b pb-3 border-blue-100">Resumo dos Dados</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h4 className="font-semibold text-gray-700 mb-4 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    Dados da Empresa
                  </h4>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <dl className="space-y-2">
                      <div className="flex justify-between py-2 border-b border-gray-200">
                        <dt className="text-gray-600">Empresa:</dt>
                        <dd className="font-medium text-gray-800">{watch('name')}</dd>
                      </div>
                      <div className="flex justify-between py-2 border-b border-gray-200">
                        <dt className="text-gray-600">Responsável:</dt>
                        <dd className="font-medium text-gray-800">{watch('responsible')}</dd>
                      </div>
                      <div className="flex justify-between py-2">
                        <dt className="text-gray-600">Data de Início:</dt>
                        <dd className="font-medium text-gray-800">{new Date(watch('startDate')).toLocaleDateString('pt-PT')}</dd>
                      </div>
                    </dl>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-700 mb-4 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Parâmetros Financeiros
                  </h4>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <dl className="space-y-2">
                      <div className="flex justify-between py-2 border-b border-gray-200">
                        <dt className="text-gray-600">Faturação Anual:</dt>
                        <dd className="font-medium text-gray-800">{fmt.format(watch('annualRevenue') || 0)}</dd>
                      </div>
                      <div className="flex justify-between py-2 border-b border-gray-200">
                        <dt className="text-gray-600">Custos Anuais:</dt>
                        <dd className="font-medium text-gray-800">{fmt.format(watch('annualCosts') || 0)}</dd>
                      </div>
                      <div className="flex justify-between py-2 border-b border-gray-200">
                        <dt className="text-gray-600">Custos Detalhados:</dt>
                        <dd className="font-medium text-gray-800">
                          <button
                            type="button"
                            onClick={() => setStep(3)}
                            className="text-blue-600 hover:text-blue-800 underline"
                          >
                            Ver detalhes ({Object.keys(detailedCosts).length} categorias)
                          </button>
                        </dd>
                      </div>
                      <div className="flex justify-between py-2">
                        <dt className="text-gray-600">Tipo de Atividade:</dt>
                        <dd className="font-medium text-gray-800">
                          {watch('activityType') === 'services' ? 'Prestação de Serviços' :
                           watch('activityType') === 'commerce' ? 'Comercial' : 'Industrial'}
                        </dd>
                      </div>
                    </dl>
                  </div>
                </div>
              </div>

              <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h4 className="font-semibold text-gray-700 mb-4 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    Regimes Fiscais
                  </h4>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <dl className="space-y-2">
                      <div className="flex justify-between py-2 border-b border-gray-200">
                        <dt className="text-gray-600">Regime de IVA:</dt>
                        <dd className="font-medium text-gray-800">
                          {watch('vatRegime') === 'normalMonthly' ? 'Normal (Mensal)' :
                           watch('vatRegime') === 'normalQuarterly' ? 'Normal (Trimestral)' :
                           'Isento (Art.º 53.º)'}
                        </dd>
                      </div>
                      <div className="flex justify-between py-2">
                        <dt className="text-gray-600">Estruturas a Simular:</dt>
                        <dd className="font-medium text-gray-800 text-right">
                          {watch('legalStructures')?.map((structure: string, index: number) => (
                            <span key={structure} className="block">
                              {structure.replace('ENI_', 'ENI ').replace('SociedadePorQuotas', 'Sociedade por Quotas')}
                            </span>
                          ))}
                        </dd>
                      </div>
                    </dl>
                  </div>
                </div>

                {showSalaryOptions && (
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-4 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                      Opções de Remuneração
                    </h4>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <dl className="space-y-2">
                        <div className="flex justify-between py-2 border-b border-gray-200">
                          <dt className="text-gray-600">Salário Mensal:</dt>
                          <dd className="font-medium text-gray-800">{fmt.format(watch('salaryMonthly') || 0)}</dd>
                        </div>
                        {watch('hasOtherSocialSecurity') !== undefined && (
                          <div className="flex justify-between py-2">
                            <dt className="text-gray-600">Outra Seg. Social:</dt>
                            <dd className="font-medium text-gray-800">{watch('hasOtherSocialSecurity') ? 'Sim' : 'Não'}</dd>
                          </div>
                        )}
                      </dl>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Alertas e Avisos */}
            {watch('vatRegime') === 'exempt53' && watch('annualRevenue') > 12000 && (
              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <div className="flex items-start">
                  <svg className="h-6 w-6 text-yellow-500 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                  <div>
                    <h4 className="font-semibold text-yellow-800">Atenção ao Limiar de IVA</h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      A sua faturação anual estimada ({fmt.format(watch('annualRevenue'))}) está próxima do limiar de isenção de IVA (€15.000).
                      Se exceder este valor, poderá ter de transitar para o regime normal de IVA.
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
              <div className="flex items-start">
                <svg className="h-8 w-8 text-blue-500 mr-4 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h4 className="font-semibold text-blue-800 text-lg mb-2">Pronto para Simular</h4>
                  <p className="text-blue-700">
                    Todos os dados foram verificados e estão prontos para a simulação. Ao clicar em "Simular Agora",
                    o sistema irá calcular os resultados fiscais para todas as estruturas selecionadas com base nos
                    parâmetros fornecidos e na legislação fiscal de 2025.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-center mt-10">
              <button
                type="submit"
                className="px-10 py-5 bg-gradient-to-r from-blue-600 to-indigo-600 text-white text-lg rounded-xl hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-300 transition-all duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                Simular Agora
              </button>
            </div>
          </div>
        );
    }
  };

  const onSubmit = (data: any) => {
    const adjustedRevenue = data.annualRevenue * (data.sensitivity / 100);
    const startDate = new Date(data.startDate);
    const isFirstYear = new Date().getTime() - startDate.getTime() < 365 * 24 * 60 * 60 * 1000;

    const params: FiscalParams = {
      activityType: data.activityType as any,
      vatRegime: data.vatRegime as any,
      vatType: 'normal',
      legalStructures: data.legalStructures as any[],
      salaryMonthly: data.salaryMonthly || 0,
      salaryDistribution: data.salaryDistribution,
      salaryRatio: data.salaryRatio,

      hasOtherSocialSecurity: data.hasOtherSocialSecurity || false,
      withholdingTaxRate: data.withholdingTaxRate || 0,
      isFirstYear,
      annualRevenue: adjustedRevenue,
      annualCosts: data.annualCosts,
      detailedCosts: detailedCosts,
      vatThreshold: vatThreshold,
      serviceCoefficient: serviceCoefficient
    };

    const newResults = calculateAll(params);

    // Salvar a simulação no localStorage
    saveSimulation(data.name, data.responsible, newResults);

    // Atualizar os resultados e rolar para a seção de resultados
    setResults(newResults);

    // Rolar para a seção de resultados após um pequeno atraso para garantir que os resultados foram renderizados
    setTimeout(() => {
      if (resultsRef.current) {
        resultsRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  };

  return (
    <div>
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-800 to-blue-900 text-white relative overflow-hidden">
        {/* Subtle background elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
          <div className="absolute top-0 right-0 w-full h-96 bg-blue-600/5 rounded-full filter blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-full h-96 bg-blue-700/5 rounded-full filter blur-3xl"></div>
        </div>
        {/* Overlay to improve text contrast */}
        <div className="absolute inset-0 bg-black/10"></div>

        <div className="container mx-auto px-4 py-20 md:py-28 relative z-10">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="inline-block px-4 py-1.5 rounded-full bg-blue-700 border border-blue-600 text-white text-sm font-medium mb-2">
                Atualizado com a Legislação Fiscal 2025
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight" style={{
                color: '#FFFFFF',
                background: 'none',
                backgroundImage: 'none',
                WebkitBackgroundClip: 'unset',
                WebkitTextFillColor: '#FFFFFF'
              }}>
                Simulador Fiscal <span style={{
                  color: '#FFFFFF',
                  background: 'none',
                  backgroundImage: 'none',
                  WebkitBackgroundClip: 'unset',
                  WebkitTextFillColor: '#FFFFFF'
                }} className="font-bold">Inteligente</span>
              </h1>
              <p className="text-xl text-blue-50 leading-relaxed">
                Otimize a sua carga fiscal e tome decisões informadas com base em simulações precisas e atualizadas. Compare diferentes regimes e estruturas jurídicas.
              </p>
              <div className="flex flex-wrap gap-4">
                <a href="#simulation-form" className="group bg-white text-blue-800 hover:bg-blue-50 px-8 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center">
                  Iniciar Simulação
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </a>
                <button className="bg-blue-700 border border-blue-600 text-white hover:bg-blue-600 px-8 py-4 rounded-xl font-semibold transition-all duration-300 flex items-center shadow-md">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Saber Mais
                </button>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="bg-blue-700/20 backdrop-blur-sm p-8 rounded-2xl shadow-xl border border-blue-600/30 transform hover:scale-102 transition-transform duration-500">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white/10 p-5 rounded-xl border border-blue-600/20 transform transition-transform hover:scale-105 hover:shadow-lg">
                    <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mb-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <h3 className="font-semibold text-lg mb-2 text-white">Empresários em Nome Individual</h3>
                    <p className="text-sm text-blue-100">Regime simplificado ou contabilidade organizada</p>
                  </div>
                  <div className="bg-white/10 p-5 rounded-xl border border-blue-600/20 transform transition-transform hover:scale-105 hover:shadow-lg">
                    <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mb-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <h3 className="font-semibold text-lg mb-2 text-white">Sociedades Unipessoais</h3>
                    <p className="text-sm text-blue-100">Otimização salarial</p>
                  </div>
                  <div className="bg-white/10 p-5 rounded-xl border border-blue-600/20 transform transition-transform hover:scale-105 hover:shadow-lg">
                    <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mb-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                    <h3 className="font-semibold text-lg mb-2 text-white">Sociedades por Quotas</h3>
                    <p className="text-sm text-blue-100">Distribuição de lucros e remunerações</p>
                  </div>
                  <div className="bg-white/10 p-5 rounded-xl border border-blue-600/20 transform transition-transform hover:scale-105 hover:shadow-lg">
                    <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mb-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <h3 className="font-semibold text-lg mb-2 text-white">Análise Comparativa</h3>
                    <p className="text-sm text-blue-100">Descubra a melhor opção para o seu negócio</p>
                  </div>
                </div>
                <div className="mt-6 bg-blue-600/30 p-4 rounded-lg border border-blue-600/30">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-medium text-white">Atualizado com a legislação fiscal mais recente</h4>
                      <p className="text-xs text-blue-100 mt-1">Incluindo alterações do Orçamento de Estado 2025</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-gray-50 py-24">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <span className="bg-blue-100 text-blue-800 px-4 py-1 rounded-full font-semibold text-sm uppercase tracking-wider">Funcionalidades Exclusivas</span>
            <h2 className="text-3xl md:text-4xl font-bold mt-4 mb-4 text-gray-800">Porquê Escolher o SimFiscal?</h2>
            <p className="text-gray-600 text-lg">A plataforma mais completa e precisa para simulações fiscais em Portugal, desenvolvida por especialistas em fiscalidade e tecnologia.</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:border-blue-200 hover:-translate-y-1">
              <div className="w-16 h-16 bg-blue-700 text-white rounded-2xl flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Cálculos Precisos</h3>
              <p className="text-gray-600 mb-4">Algoritmos atualizados com a legislação fiscal portuguesa mais recente, incluindo todas as alterações do Orçamento de Estado 2025.</p>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <svg className="h-5 w-5 text-blue-600 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Tabelas de IRS atualizadas
                </li>
                <li className="flex items-start">
                  <svg className="h-5 w-5 text-blue-600 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Cálculos de Segurança Social
                </li>
                <li className="flex items-start">
                  <svg className="h-5 w-5 text-blue-600 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Regimes de IVA e isenções
                </li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:border-blue-200 hover:-translate-y-1">
              <div className="w-16 h-16 bg-blue-700 text-white rounded-2xl flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Análise Comparativa</h3>
              <p className="text-gray-600 mb-4">Compare diferentes estruturas jurídicas e regimes fiscais lado a lado, com visualizações claras e detalhadas para tomada de decisão.</p>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <svg className="h-5 w-5 text-blue-600 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Comparação de múltiplos regimes
                </li>
                <li className="flex items-start">
                  <svg className="h-5 w-5 text-blue-600 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Gráficos interativos
                </li>
                <li className="flex items-start">
                  <svg className="h-5 w-5 text-blue-600 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Recomendações personalizadas
                </li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:border-blue-200 hover:-translate-y-1">
              <div className="w-16 h-16 bg-blue-700 text-white rounded-2xl flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Relatórios Profissionais</h3>
              <p className="text-gray-600 mb-4">Exporte relatórios completos em PDF com análises detalhadas, gráficos e recomendações para partilhar com o seu contabilista.</p>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <svg className="h-5 w-5 text-blue-600 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Exportação em PDF de alta qualidade
                </li>
                <li className="flex items-start">
                  <svg className="h-5 w-5 text-blue-600 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Explicações detalhadas dos cálculos
                </li>
                <li className="flex items-start">
                  <svg className="h-5 w-5 text-blue-600 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Referências à legislação aplicável
                </li>
              </ul>
            </div>
          </div>

          <div className="mt-16 bg-white p-8 rounded-2xl border border-blue-100 shadow-lg">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="mb-6 md:mb-0 md:mr-8">
                <h3 className="text-2xl font-bold text-gray-800 mb-2">Atualizado com a Legislação Fiscal 2025</h3>
                <p className="text-gray-600">Todos os cálculos e simulações estão em conformidade com as mais recentes alterações fiscais em Portugal.</p>
              </div>
              <a href="#simulation-form" className="bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center whitespace-nowrap">
                Iniciar Simulação Gratuita
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Simulation Form Section */}
      <div id="simulation-form" className="container mx-auto px-4 py-24 scroll-mt-24" ref={simulationFormRef}>
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block px-3 py-1 rounded-full bg-blue-100 text-blue-600 text-sm font-medium mb-3">Simulação Personalizada</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Inicie a Sua Simulação Fiscal</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">Preencha os dados abaixo para obter uma análise fiscal detalhada e personalizada para o seu negócio</p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100">
            <div className="bg-gradient-to-r from-blue-800 to-blue-900 p-10 relative overflow-hidden">
              {/* Background pattern */}
              <div className="absolute inset-0 opacity-5">
                <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                  <defs>
                    <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                      <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" strokeWidth="0.5" />
                    </pattern>
                  </defs>
                  <rect width="100" height="100" fill="url(#grid)" />
                </svg>
              </div>

              {/* Step progress */}
              <div className="steps-container relative z-10">
                {[1, 2, 3, 4, 5].map((s) => (
                  <div key={s} className="relative">
                    <button
                      type="button"
                      onClick={() => {
                        setStep(s);
                        // Rolar para o topo do formulário de simulação
                        if (simulationFormRef.current) {
                          simulationFormRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        }
                      }}
                      className={`step-button ${step === s ? 'active' : step > s ? 'completed' : ''} relative`}
                    >
                      {step > s ? (
                        <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <span className="relative z-10">{s}</span>
                      )}
                    </button>
                    <div className={`absolute -bottom-8 left-1/2 transform -translate-x-1/2 whitespace-nowrap text-sm font-medium transition-all duration-300 ${step === s ? 'text-white' : 'text-blue-200'}`}>
                      {s === 1 && "Dados da Empresa"}
                      {s === 2 && "Parâmetros Fiscais"}
                      {s === 3 && "Custos Detalhados"}
                      {s === 4 && "Escolha de Regimes"}
                      {s === 5 && "Revisão & Simular"}
                    </div>
                  </div>
                ))}
              </div>

              {/* Progress bar */}
              <div className="mt-12 relative h-1 bg-blue-400/30 rounded-full overflow-hidden">
                <div
                  className="absolute top-0 left-0 h-full bg-white rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${(step / 5) * 100}%` }}
                ></div>
              </div>
            </div>

            <div className="p-8 md:p-10">
              <div className="bg-blue-50 rounded-xl p-4 mb-8 border border-blue-100">
                <div className="flex items-start">
                  <svg className="h-6 w-6 text-blue-500 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <h4 className="font-semibold text-blue-800">Informação Importante</h4>
                    <p className="text-sm text-blue-700">Todos os cálculos são baseados na legislação fiscal portuguesa atual. Os resultados são indicativos e não substituem o aconselhamento profissional.</p>
                  </div>
                </div>
              </div>

              {renderStep()}

              <div className="flex justify-between mt-12">
                <div>
                  {step > 1 && (
                    <button
                      type="button"
                      onClick={() => {
                        setStep(step - 1);
                        // Rolar para o topo do formulário de simulação
                        setTimeout(() => {
                          if (simulationFormRef.current) {
                            simulationFormRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
                          }
                        }, 100);
                      }}
                      className="group px-8 py-3 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-300 transition-all duration-200 shadow-sm flex items-center"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                      Anterior
                    </button>
                  )}
                </div>
                <div>
                  {step < 5 ? (
                    <button
                      type="button"
                      onClick={() => {
                        setStep(step + 1);
                        // Rolar para o topo do formulário de simulação
                        setTimeout(() => {
                          if (simulationFormRef.current) {
                            simulationFormRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
                          }
                        }, 100);
                      }}
                      className="group px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-300 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 flex items-center"
                    >
                      Próximo
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  ) : null}
                </div>
              </div>
            </div>
          </form>

          <div className="mt-8 text-center text-gray-500 text-sm">
            <p>Todos os dados são processados localmente e não são armazenados em servidores externos.</p>
          </div>
        </div>
      </div>

      {/* Seção de Resultados */}
      <div className="bg-gradient-to-b from-gray-50 to-white py-24" ref={resultsRef} id="results-section">
        {results && results.length > 0 ? (
          <div className="container mx-auto px-4">
            <div className="text-center max-w-3xl mx-auto mb-16">
              <span className="inline-block px-3 py-1 rounded-full bg-green-100 text-green-600 text-sm font-medium mb-3">Análise Completa</span>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Resultados da Simulação</h2>
              <p className="text-xl text-gray-600">Análise detalhada das diferentes estruturas jurídicas e regimes fiscais para o seu negócio</p>
            </div>

            {/* Alertas de Limiar de IVA */}
            {watch('vatRegime') === 'exempt53' && watch('annualRevenue') > 0 && (
              <VATThresholdAlert
                annualRevenue={watch('annualRevenue')}
                vatRegime={watch('vatRegime')}
              />
            )}

            {/* Dashboard Fiscal */}
            <FiscalDashboard results={results} />

            {/* Recomendações Fiscais */}
            <FiscalRecommendations params={watch()} results={results} />

            {/* Comparison Table */}
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden mb-16 border border-gray-100">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6">
                <h3 className="text-xl font-bold">Comparação de Estruturas Jurídicas</h3>
                <p className="text-blue-100 mt-1">Análise lado a lado de todas as opções simuladas</p>
              </div>
              <div className="p-6">
                <ComparisonTable results={results} />
              </div>
            </div>

            {/* Charts Section */}
            <div className="mb-16">
              <h3 className="text-2xl font-bold mb-8 text-center">Análise Visual Comparativa</h3>

              <div className="grid md:grid-cols-2 gap-8 mb-12">
                <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
                  <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
                    <h3 className="text-xl font-bold">Rendimento Líquido</h3>
                    <p className="text-blue-100 text-sm mt-1">Comparação do valor que efetivamente fica disponível</p>
                  </div>
                  <div className="p-6 bar-chart">
                    <BarChart
                      data={results.map(r => ({
                        label: r.structure.replace('ENI_', 'ENI ').replace('SociedadePorQuotas', 'Lda.'),
                        value: r.netProfit
                      }))}
                      yAxisLabel="Euros (€)"
                      title="Comparação de Rendimento Líquido"
                    />
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
                  <div className="bg-gradient-to-r from-red-600 to-red-700 text-white p-6">
                    <h3 className="text-xl font-bold">Carga Fiscal</h3>
                    <p className="text-red-100 text-sm mt-1">Comparação do total de impostos e contribuições</p>
                  </div>
                  <div className="p-6 bar-chart">
                    <BarChart
                      data={results.map(r => ({
                        label: r.structure.replace('ENI_', 'ENI ').replace('SociedadePorQuotas', 'Lda.'),
                        value: r.incomeTax + r.socialSecurity
                      }))}
                      yAxisLabel="Euros (€)"
                      title="Comparação de Carga Fiscal"
                    />
                  </div>
                </div>
              </div>

              <h3 className="text-xl font-bold mb-6 text-center">Distribuição da Carga Fiscal por Estrutura</h3>
              <div className="grid md:grid-cols-3 gap-8 mb-12">
                {results.map((result, index) => (
                  <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
                    <div className={`p-6 text-white ${result === results.reduce((prev, current) => (prev.netProfit > current.netProfit) ? prev : current) ? 'bg-gradient-to-r from-green-600 to-green-700' : 'bg-gradient-to-r from-blue-600 to-indigo-600'}`}>
                      <h3 className="text-xl font-bold">{result.structure.replace('ENI_', 'ENI ').replace('SociedadePorQuotas', 'Lda.')}</h3>
                      <p className="text-sm mt-1 text-white/80">Taxa efetiva: {(result.incomeTax / result.taxableProfit * 100).toFixed(1)}%</p>
                    </div>
                    <div className="p-6 pie-chart">
                      <PieChart
                        data={[
                          { label: 'IRS', value: result.incomeTax },
                          { label: 'SS', value: result.socialSecurity }
                        ]}
                        title="Distribuição da Carga Fiscal"
                      />
                      <div className="mt-4 grid grid-cols-2 gap-2 text-sm">
                        <div className="flex items-center">
                          <span className="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                          <span className="text-gray-600">IRS: {fmt.format(result.incomeTax)}</span>
                        </div>
                        <div className="flex items-center">
                          <span className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                          <span className="text-gray-600">SS: {fmt.format(result.socialSecurity)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Considerations Section */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden mb-16 border border-gray-100">
              <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-6">
                <h3 className="text-xl font-bold">Considerações Importantes</h3>
                <p className="text-indigo-100 mt-1">Fatores adicionais a ter em conta na sua decisão</p>
              </div>
              <div className="p-8">
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                    <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    <h4 className="text-lg font-semibold mb-2">Complexidade Administrativa</h4>
                    <p className="text-gray-600 text-sm">
                      Sociedades requerem mais obrigações contabilísticas e declarativas comparado com empresários em nome individual.
                    </p>
                  </div>

                  <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                    <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <h4 className="text-lg font-semibold mb-2">Responsabilidade</h4>
                    <p className="text-gray-600 text-sm">
                      Em sociedades, a responsabilidade é limitada ao capital social, enquanto ENI responde com património pessoal.
                    </p>
                  </div>

                  <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                    <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h4 className="text-lg font-semibold mb-2">Perspetiva de Longo Prazo</h4>
                    <p className="text-gray-600 text-sm">
                      Considere a evolução esperada do negócio nos próximos anos, pois a estrutura ideal pode mudar com o crescimento.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Parameters Section */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden mb-16 border border-gray-100">
              <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
                <h3 className="text-xl font-bold">Parâmetros Fiscais</h3>
                <p className="text-blue-100 mt-1">Ajuste os parâmetros para refinar a sua simulação</p>
              </div>
              <div className="p-8">
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <label className="block text-gray-700 font-semibold mb-3">Limiar de Isenção de IVA (€)</label>
                    <div className="flex items-center">
                      <input
                        type="number"
                        value={vatThreshold}
                        onChange={(e) => setVatThreshold(Number(e.target.value))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      />
                      <button
                        onClick={() => setVatThreshold(15000)}
                        className="ml-2 px-4 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
                      >
                        Reset
                      </button>
                    </div>
                    <div className="mt-3 bg-blue-50 p-3 rounded-lg border border-blue-100">
                      <div className="flex items-start">
                        <svg className="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div>
                          <p className="text-sm text-blue-800">Valor legal atual: €15.000 (Art.º 53.º CIVA)</p>
                          <p className="text-xs text-blue-600 mt-1">Abaixo deste valor, os sujeitos passivos podem optar pelo regime de isenção de IVA.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="block text-gray-700 font-semibold mb-3">Coeficiente para Serviços (%)</label>
                    <div className="flex items-center">
                      <input
                        type="number"
                        value={serviceCoefficient * 100}
                        onChange={(e) => setServiceCoefficient(Number(e.target.value) / 100)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      />
                      <button
                        onClick={() => setServiceCoefficient(0.75)}
                        className="ml-2 px-4 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
                      >
                        Reset
                      </button>
                    </div>
                    <div className="mt-3 bg-blue-50 p-3 rounded-lg border border-blue-100">
                      <div className="flex items-start">
                        <svg className="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div>
                          <p className="text-sm text-blue-800">Valor legal atual: 75% (Art.º 31.º CIRS)</p>
                          <p className="text-xs text-blue-600 mt-1">Aplicável a prestações de serviços no regime simplificado de IRS.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Export Section */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-8 flex flex-col md:flex-row items-center justify-between border border-blue-100 mb-16">
              <div className="mb-6 md:mb-0">
                <h3 className="text-xl font-bold text-gray-800 mb-2">Exportar Relatório Detalhado</h3>
                <p className="text-gray-600">Obtenha um relatório completo em PDF com todas as análises e recomendações</p>
              </div>
              <button
                onClick={() => exportToPDF(results, watch('name'), watch('responsible'))}
                className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-4 rounded-xl transition-all duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Exportar Relatório Completo
              </button>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
}