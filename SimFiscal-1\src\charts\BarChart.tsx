// src/components/charts/BarChart.tsx
import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

interface ChartDataItem {
  label: string;
  value: number;
}

interface Props {
  data: ChartDataItem[];
  title?: string;
  yAxisLabel?: string;
}

export function BarChart({ data, title, yAxisLabel }: Props) {
  const labels = data.map((item) => item.label);
  const values = data.map((item) => item.value);

  // Criar um array de cores para cada barra, destacando as simulações recomendadas
  const backgroundColors = data.map(item => {
    // Se o label contém ★, é uma simulação recomendada
    if (item.label.includes('★')) {
      return 'rgba(34, 197, 94, 0.8)'; // Verde para recomendado
    }
    return 'rgba(59, 130, 246, 0.7)'; // Azul para as demais
  });

  const borderColors = data.map(item => {
    if (item.label.includes('★')) {
      return 'rgba(21, 128, 61, 1)'; // Verde escuro para recomendado
    }
    return 'rgba(37, 99, 235, 1)'; // Azul escuro para as demais
  });

  const chartData = {
    labels,
    datasets: [
      {
        label: title || 'Valor',
        data: values,
        backgroundColor: backgroundColors,
        borderColor: borderColors,
        borderWidth: 1,
        borderRadius: 6,
        hoverBackgroundColor: data.map(item =>
          item.label.includes('★') ? 'rgba(34, 197, 94, 0.9)' : 'rgba(59, 130, 246, 0.9)'
        ),
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            // Verificar se o eixo Y é percentagem ou valor monetário
            if (yAxisLabel && yAxisLabel.toLowerCase().includes('percentagem')) {
              return context.parsed.y.toFixed(1) + '%';
            } else {
              return new Intl.NumberFormat('pt-PT', {
                style: 'currency',
                currency: 'EUR'
              }).format(context.parsed.y);
            }
          },
          title: function(context) {
            // Remover o símbolo ★ do título do tooltip
            return context[0].label.replace('★', '(Recomendado)');
          }
        },
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        padding: 12,
        titleFont: {
          size: 14,
          weight: 'bold'
        },
        bodyFont: {
          size: 14
        },
        cornerRadius: 6
      },
      title: {
        display: !!title,
        text: title || '',
        font: {
          size: 16,
          weight: 'bold'
        },
        padding: {
          bottom: 20
        },
        color: '#1e3a8a' // Azul escuro
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: !!yAxisLabel,
          text: yAxisLabel || '',
          font: {
            weight: 'bold',
            size: 12
          },
          color: '#64748b' // Cinza azulado
        },
        grid: {
          color: 'rgba(226, 232, 240, 0.6)' // Linhas de grade mais suaves
        },
        ticks: {
          callback: function(value) {
            // Verificar se o eixo Y é percentagem ou valor monetário
            if (yAxisLabel && yAxisLabel.toLowerCase().includes('percentagem')) {
              return value + '%';
            } else {
              return new Intl.NumberFormat('pt-PT', {
                style: 'currency',
                currency: 'EUR',
                maximumFractionDigits: 0
              }).format(value);
            }
          },
          font: {
            size: 11
          },
          color: '#64748b' // Cinza azulado
        }
      },
      x: {
        grid: {
          display: false // Remover linhas de grade verticais
        },
        ticks: {
          font: {
            size: 12,
            weight: 'bold'
          },
          color: '#334155' // Cinza escuro
        }
      }
    }
  };

  return (
    <div style={{ height: '300px' }}>
      <Bar options={options} data={chartData} />
    </div>
  );
}