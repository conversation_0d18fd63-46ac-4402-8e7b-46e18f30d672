// src/utils/formatters.ts

/**
 * Formata um valor numérico para moeda (EUR)
 */
export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-PT', {
    style: 'currency',
    currency: 'EUR'
  }).format(value);
};

/**
 * Formata um valor numérico para percentagem
 */
export const formatPercent = (value: number): string => {
  return new Intl.NumberFormat('pt-PT', {
    style: 'percent',
    minimumFractionDigits: 1,
    maximumFractionDigits: 1
  }).format(value / 100);
};

/**
 * Formata um valor numérico para número com separador de milhares
 */
export const formatNumber = (value: number): string => {
  return new Intl.NumberFormat('pt-PT').format(value);
};

/**
 * Formata uma data para o formato português
 */
export const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('pt-PT', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date);
};
