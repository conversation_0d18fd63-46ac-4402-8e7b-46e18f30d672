// src/components/FiscalInfo.tsx
import React from 'react';
import { generateFiscalPDF } from '../utils/generatePDF';
import {
  IRS_BRACKETS,
  VAT_RATES,
  SS_RATES,
  VAT_EXEMPTION_THRESHOLD,
  SERVICE_COEFFICIENT,
  SALES_COEFFICIENT,
  LOCAL_ACCOMMODATION_COEFFICIENT,
  FOOD_HOSPITALITY_COEFFICIENT,
  SS_SERVICE_COEFFICIENT,
  SS_SALES_COEFFICIENT,
  SS_FOOD_HOSPITALITY_COEFFICIENT,
  IRC_RATE,
  IRC_RATE_SME,
  IRC_SME_THRESHOLD,
  IAS,
  SOLIDARITY_TAX_RATES
} from '@types/TaxBrackets2025';

const FiscalInfo: React.FC = () => {
  return (
    <div id="fiscal-info" className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4 text-blue-800">Parâmetros Fiscais 2025</h2>

      <div className="mb-6">
        <h3 className="text-xl font-semibold mb-2 text-blue-700">IRS</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-300">
            <thead>
              <tr className="bg-blue-100">
                <th className="py-2 px-4 border-b">Escalão</th>
                <th className="py-2 px-4 border-b">Rendimento Coletável (€)</th>
                <th className="py-2 px-4 border-b">Taxa (%)</th>
                <th className="py-2 px-4 border-b">Parcela a Abater (€)</th>
              </tr>
            </thead>
            <tbody>
              {IRS_BRACKETS.map((bracket, index) => (
                <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                  <td className="py-2 px-4 border-b text-center">{index + 1}º</td>
                  <td className="py-2 px-4 border-b">
                    {bracket.min === 0
                      ? `Até ${bracket.max.toLocaleString('pt-PT')}`
                      : bracket.max === Infinity
                        ? `Superior a ${bracket.min.toLocaleString('pt-PT')}`
                        : `De ${bracket.min.toLocaleString('pt-PT')} a ${bracket.max.toLocaleString('pt-PT')}`
                    }
                  </td>
                  <td className="py-2 px-4 border-b text-center">{(bracket.rate * 100).toFixed(1)}</td>
                  <td className="py-2 px-4 border-b text-right">{bracket.deduction.toLocaleString('pt-PT', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-xl font-semibold mb-2 text-blue-700">Taxa Adicional de Solidariedade</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-300">
            <thead>
              <tr className="bg-blue-100">
                <th className="py-2 px-4 border-b">Rendimento Coletável (€)</th>
                <th className="py-2 px-4 border-b">Taxa (%)</th>
              </tr>
            </thead>
            <tbody>
              {SOLIDARITY_TAX_RATES.map((bracket, index) => (
                <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                  <td className="py-2 px-4 border-b">
                    {bracket.max === Infinity
                      ? `Superior a ${bracket.min.toLocaleString('pt-PT')}`
                      : `De ${bracket.min.toLocaleString('pt-PT')} a ${bracket.max.toLocaleString('pt-PT')}`
                    }
                  </td>
                  <td className="py-2 px-4 border-b text-center">{(bracket.rate * 100).toFixed(1)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-xl font-semibold mb-2 text-blue-700">IRC</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="font-medium">Taxa geral: <span className="font-bold">{(IRC_RATE * 100).toFixed(0)}%</span></p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="font-medium">Taxa para PMEs (até {IRC_SME_THRESHOLD.toLocaleString('pt-PT')}€): <span className="font-bold">{(IRC_RATE_SME * 100).toFixed(0)}%</span></p>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-xl font-semibold mb-2 text-blue-700">IVA</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          {VAT_RATES.map((rate, index) => (
            <div key={index} className="bg-gray-50 p-4 rounded-md">
              <p className="font-medium">Taxa {rate.type === 'reduced' ? 'reduzida' : rate.type === 'intermediate' ? 'intermédia' : 'normal'}: <span className="font-bold">{(rate.rate * 100).toFixed(0)}%</span></p>
            </div>
          ))}
        </div>
        <div className="bg-blue-50 p-4 rounded-md">
          <p className="font-medium">Limiar de isenção: <span className="font-bold">{VAT_EXEMPTION_THRESHOLD.toLocaleString('pt-PT')}€</span></p>
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-xl font-semibold mb-2 text-blue-700">Segurança Social</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="font-medium">Trabalhadores independentes: <span className="font-bold">{(SS_RATES.independent * 100).toFixed(2)}%</span></p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="font-medium">Taxa reduzida: <span className="font-bold">{(SS_RATES.independent_reduced * 100).toFixed(1)}%</span></p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="font-medium">Empresários em nome individual: <span className="font-bold">{(SS_RATES.independent_entrepreneur * 100).toFixed(1)}%</span></p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="font-medium">Entidades empregadoras: <span className="font-bold">{(SS_RATES.company * 100).toFixed(2)}%</span></p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="font-medium">Trabalhadores por conta de outrem: <span className="font-bold">{(SS_RATES.employee * 100).toFixed(0)}%</span></p>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-xl font-semibold mb-2 text-blue-700">Regime Simplificado</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="font-medium">Prestação de serviços: <span className="font-bold">{(SERVICE_COEFFICIENT * 100).toFixed(0)}%</span></p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="font-medium">Vendas de mercadorias e produtos: <span className="font-bold">{(SALES_COEFFICIENT * 100).toFixed(0)}%</span></p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="font-medium">Alojamento local (moradia e apartamento): <span className="font-bold">{(LOCAL_ACCOMMODATION_COEFFICIENT * 100).toFixed(0)}%</span></p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="font-medium">Restauração, bebidas e hotelaria: <span className="font-bold">{(FOOD_HOSPITALITY_COEFFICIENT * 100).toFixed(0)}%</span></p>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h3 className="text-xl font-semibold mb-2 text-blue-700">Indexante dos Apoios Sociais (IAS)</h3>
        <div className="bg-blue-50 p-4 rounded-md">
          <p className="font-medium">Valor do IAS: <span className="font-bold">{IAS.toLocaleString('pt-PT', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}€</span></p>
        </div>
      </div>

      <div className="mt-8 text-sm text-gray-600">
        <p>Nota: Estes valores são baseados na legislação fiscal portuguesa para 2025. Recomenda-se sempre a consulta da legislação em vigor para confirmação dos valores.</p>
      </div>
    </div>
  );
};

export default FiscalInfo;
