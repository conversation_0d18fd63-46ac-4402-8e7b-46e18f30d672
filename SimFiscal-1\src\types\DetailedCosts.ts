// src/types/DetailedCosts.ts

/**
 * Representa uma categoria de custo com valores mensais e anuais
 */
export interface CostCategory {
  /** Descrição da categoria de custo */
  description: string;
  /** Valor mensal do custo */
  monthlyValue: number;
  /** Valor anual do custo (calculado automaticamente) */
  annualValue: number;
  /** Se o IVA é dedutível para esta categoria */
  vatDeductible: boolean | string;
}

/**
 * Estrutura para armazenar todas as categorias de custos detalhados
 */
export interface DetailedCosts {
  /** Custos relacionados com imobilizado */
  vehicleAssets: CostCategory;
  otherAssets: CostCategory;
  
  /** Custos de serviços */
  securityServices: CostCategory;
  marketingServices: CostCategory;
  greenReceipts: CostCategory;
  specializedServices: CostCategory;
  legalServices: CostCategory;
  accountingServices: CostCategory;
  
  /** Custos bancários e financeiros */
  bankFees: CostCategory;
  
  /** Custos de manutenção */
  maintenanceRepair: CostCategory;
  construction: CostCategory;
  
  /** Custos de materiais e consumíveis */
  toolsUtensils: CostCategory;
  decorationItems: CostCategory;
  booksDocumentation: CostCategory;
  officeSupplies: CostCategory;
  giftItems: CostCategory;
  
  /** Custos de energia e utilidades */
  electricity: CostCategory;
  gas: CostCategory;
  fuel: CostCategory;
  
  /** Custos de deslocações e estadas */
  travelAccommodation: CostCategory;
  travelTickets: CostCategory;
  travelTolls: CostCategory;
  otherTransport: CostCategory;
  
  /** Custos de rendas e alugueres */
  rentals: CostCategory;
  
  /** Custos de comunicação */
  mobilePhone: CostCategory;
  internet: CostCategory;
  cableTv: CostCategory;
  courierServices: CostCategory;
  
  /** Custos de seguros */
  insurance: CostCategory;
  
  /** Custos administrativos */
  notaryFees: CostCategory;
  
  /** Custos de higiene e limpeza */
  cleaningSupplies: CostCategory;
  
  /** Custos com pessoal */
  salaries: CostCategory;
  socialSecurity: CostCategory;
  bonuses: CostCategory;
  mealAllowance: CostCategory;
  trainingEvents: CostCategory;
  educationAllowance: CostCategory;
  otherSalaryComponents: CostCategory;
  
  /** Outros custos */
  donations: CostCategory;
  christmasItems: CostCategory;
  giftCards: CostCategory;
  footwear: CostCategory;
  hairdressing: CostCategory;
  clothing: CostCategory;
  groceriesCleaning: CostCategory;
  groceriesOffice: CostCategory;
  groceriesHome: CostCategory;
}

/**
 * Cria uma estrutura de custos detalhados com valores padrão
 */
export function createEmptyDetailedCosts(): DetailedCosts {
  // Função auxiliar para criar uma categoria de custo vazia
  const createEmptyCategory = (description: string, vatDeductible: boolean | string = false): CostCategory => ({
    description,
    monthlyValue: 0,
    annualValue: 0,
    vatDeductible
  });

  return {
    // Imobilizado
    vehicleAssets: createEmptyCategory("Imobilizado Viatura", "Comerciais / Híbridos / Elétricos"),
    otherAssets: createEmptyCategory("Imobilizado outros (eletrod., equip., mobiliário, comput.)", "Sim"),
    
    // Serviços
    securityServices: createEmptyCategory("Vigilância e segurança", "Sim"),
    marketingServices: createEmptyCategory("Publicidade e marketing", "Sim"),
    greenReceipts: createEmptyCategory("Recibos verdes de serviços (incl ato isolado)", "Sim"),
    specializedServices: createEmptyCategory("Outros Trab. Especializado Subcontratados", "Sim"),
    legalServices: createEmptyCategory("Serviços jurídicos", "Sim"),
    accountingServices: createEmptyCategory("Contabilidade", "Sim"),
    
    // Custos bancários
    bankFees: createEmptyCategory("Despesas bancárias", "Isento (0%), não tem iva"),
    
    // Manutenção
    maintenanceRepair: createEmptyCategory("Conservação e reparações (automov. e equipamentos)", "Sim"),
    construction: createEmptyCategory("Obras", "Sim"),
    
    // Materiais e consumíveis
    toolsUtensils: createEmptyCategory("Ferramentas e utensílios", "Sim"),
    decorationItems: createEmptyCategory("artigos de decoração", "Sim"),
    booksDocumentation: createEmptyCategory("Livros, revistas e documentação técnica", "Sim"),
    officeSupplies: createEmptyCategory("Material de escritório (inform., escrit etc)", "Sim"),
    giftItems: createEmptyCategory("Artigos p/ Oferta (ex: Natal)", "Sim"),
    
    // Energia e utilidades
    electricity: createEmptyCategory("Eletricidade", "Sim"),
    gas: createEmptyCategory("Gás", "Sim"),
    fuel: createEmptyCategory("Gasóleo e gasolina (viat. imob.)", "Gasóleo Não-deduz / Diesel 50%"),
    
    // Deslocações e estadas
    travelAccommodation: createEmptyCategory("Deslocações e estadas - hotéis e viagens", "Não"),
    travelTickets: createEmptyCategory("Deslocações e estadas - bilhetes e passes", "Não / Sim"),
    travelTolls: createEmptyCategory("Deslocações e estadas - portagens", "Não"),
    otherTransport: createEmptyCategory("Transportes diversos", "Sim"),
    
    // Rendas e alugueres
    rentals: createEmptyCategory("Rendas e alugueres (edif. Lojas, equipamentos, viaturas)", "Sim"),
    
    // Comunicação
    mobilePhone: createEmptyCategory("Comunicação - Telemóvel e telefone", "Sim"),
    internet: createEmptyCategory("Comunicação - Internet", "Sim"),
    cableTv: createEmptyCategory("Comunicação - TV cabo", "Sim"),
    courierServices: createEmptyCategory("Comunicação - CTT e outros transportadores (mercad.)", "Sim"),
    
    // Seguros
    insurance: createEmptyCategory("Seguros (vida, saúde, carro, etc)", "Isento (0%), não tem iva"),
    
    // Administrativos
    notaryFees: createEmptyCategory("Despesas notariais e taxas", "Sim"),
    
    // Higiene e limpeza
    cleaningSupplies: createEmptyCategory("Limpeza higiene e conforto e vestuário", "Sim"),
    
    // Pessoal
    salaries: createEmptyCategory("Salários - valores", "Isento (0%), não tem iva"),
    socialSecurity: createEmptyCategory("Salários - Social", "na"),
    bonuses: createEmptyCategory("Salários - PRÉMIOS", "na"),
    mealAllowance: createEmptyCategory("Salários - Sub. Alimentação", "Isento (0%), não tem iva"),
    trainingEvents: createEmptyCategory("Salários - eventos, espect., formação e colóquios", "Sim"),
    educationAllowance: createEmptyCategory("Salários - educação e tickets (inf., alim., educ.)", "na"),
    otherSalaryComponents: createEmptyCategory("Salários - Sub. Complementares", "na"),
    
    // Outros
    donations: createEmptyCategory("Donativos", "na"),
    christmasItems: createEmptyCategory("Artigos de prendas/ofertas de Natal", "Sim"),
    giftCards: createEmptyCategory("Gift Cards (vales/cartões, etc)", "Sim"),
    footwear: createEmptyCategory("sapataria", "Sim"),
    hairdressing: createEmptyCategory("cabeleireiro", "Sim"),
    clothing: createEmptyCategory("Vestuário e determinada roupa", "Sim"),
    groceriesCleaning: createEmptyCategory("compras correntes de supermercado - limpeza", "Sim"),
    groceriesOffice: createEmptyCategory("compras correntes de supermercado - escrit. e ferramentas", "Sim"),
    groceriesHome: createEmptyCategory("compras correntes de supermercado - peq. Eletrodomest.", "Sim")
  };
}

/**
 * Calcula o total de todos os custos detalhados
 */
export function calculateTotalCosts(costs: DetailedCosts): number {
  return Object.values(costs).reduce((total, category) => total + category.annualValue, 0);
}
