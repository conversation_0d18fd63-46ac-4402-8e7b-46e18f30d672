
import React from 'react';
import type { SimulationResult } from '@types/SimulationResult';
import type { HistoricalSimulation, TaxBreakpoint, MultiYearProjection } from '@types/HistoricalData';
import { BarChart } from '../charts/BarChart';
import { LineChart } from '../charts/LineChart';
import { PieChart } from '../charts/PieChart';

interface Props {
  currentResults: SimulationResult[];
  historicalData: HistoricalSimulation[];
  breakpoints: TaxBreakpoint[];
  projections: MultiYearProjection[];
}

export function Dashboard({
  currentResults,
  historicalData,
  breakpoints,
  projections
}: Props) {
  const fmt = new Intl.NumberFormat('pt-PT', { style: 'currency', currency: 'EUR' });

  return (
    <div className="dashboard-container">
      <div className="grid grid-cols-2 gap-4">
        <div className="card">
          <h3>Comparação Histórica</h3>
          {historicalData.map((hist, i) => (
            <div key={i} className="historical-entry">
              <span>{new Date(hist.date).toLocaleDateString('pt-PT')}</span>
              <span>{fmt.format(hist.results[0].netProfit)}</span>
            </div>
          ))}
        </div>

        <div className="card">
          <h3>Breakpoints Fiscais</h3>
          {breakpoints.map((bp, i) => (
            <div key={i} className="breakpoint-entry">
              <span>{fmt.format(bp.value)}</span>
              <p>{bp.description}</p>
            </div>
          ))}
        </div>

        {/* Card de otimização de dividendos removido */}

        <div className="card">
          <h3>Projeção Multi-ano</h3>
          {projections.map((proj) => (
            <div key={proj.year} className="projection-entry">
              <span>{proj.year}</span>
              <span>{fmt.format(proj.results[0].netProfit)}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
