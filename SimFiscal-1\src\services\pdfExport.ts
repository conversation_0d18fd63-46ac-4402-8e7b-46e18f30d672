
import html2pdf from 'html2pdf.js';
import type { SimulationResult } from '../types/SimulationResult';
import { formatCurrency, formatPercent } from '../utils/formatters';
import { VAT_EXEMPTION_THRESHOLD, VAT_IMMEDIATE_TRANSITION_THRESHOLD } from '@types/TaxBrackets2025';

export async function exportToPDF(
  results: SimulationResult[],
  companyName: string = 'Empresa',
  responsible: string = 'Responsável'
) {
  const element = document.createElement('div');
  element.className = 'pdf-container';

  const fmt = new Intl.NumberFormat('pt-PT', { style: 'currency', currency: 'EUR' });

  const bestStructure = results.reduce((prev, curr) =>
    prev.netProfit > curr.netProfit ? prev : curr
  );

  const taxBurdens = results.map(r => ({
    structure: r.structure,
    burden: ((r.totalTax / r.taxableProfit) * 100).toFixed(1)
  }));

  // Função para gerar alerta de limiar de IVA
  const generateVATThresholdAlert = (annualRevenue: number, vatRegime: string) => {
    if (vatRegime !== 'exempt53') return '';

    if (annualRevenue > VAT_IMMEDIATE_TRANSITION_THRESHOLD) {
      return `
        <div class="alert-danger">
          <h4>Atenção: Transição Imediata para Regime Normal de IVA</h4>
          <p>A faturação anual de ${formatCurrency(annualRevenue)} excede o limiar de transição imediata de ${formatCurrency(VAT_IMMEDIATE_TRANSITION_THRESHOLD)} (125% do limiar base). Isto obriga à transição imediata para o regime normal de IVA, e a fatura que excede este valor já deve incluir IVA.</p>
        </div>
      `;
    } else if (annualRevenue > VAT_EXEMPTION_THRESHOLD) {
      return `
        <div class="alert-warning">
          <h4>Aviso: Limiar de Isenção de IVA Excedido</h4>
          <p>A faturação anual de ${formatCurrency(annualRevenue)} excede o limiar de isenção de IVA de ${formatCurrency(VAT_EXEMPTION_THRESHOLD)}. A isenção será mantida até o final do ano, mas será necessário entregar declaração de alterações no prazo de 15 dias úteis a contar do último dia do ano e passar para o regime normal a partir de 1 de janeiro do ano seguinte.</p>
        </div>
      `;
    }

    return '';
  };

  // Adicionar estilos CSS para o PDF
  const styles = `
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

      .pdf-container {
        font-family: 'Poppins', 'Helvetica', 'Arial', sans-serif;
        color: #1e293b;
        line-height: 1.6;
        background-color: #f8fafc;
      }

      .pdf-header {
        text-align: center;
        margin-bottom: 30px;
        padding: 30px 20px;
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        border-radius: 12px;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      }

      .pdf-header h1 {
        color: white;
        margin-bottom: 8px;
        font-weight: 700;
        font-size: 28px;
      }

      .pdf-header p {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        margin-bottom: 20px;
      }

      .pdf-header .company-info {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        font-size: 14px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
      }

      .pdf-header .company-info div {
        padding: 0 10px;
      }

      .pdf-header .company-info strong {
        color: white;
        font-weight: 600;
      }

      .pdf-content {
        padding: 20px;
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        margin: 20px 0;
      }

      .pdf-content h2 {
        color: #1e40af;
        border-bottom: 2px solid #e2e8f0;
        padding-bottom: 10px;
        margin-top: 30px;
        font-weight: 700;
        font-size: 22px;
      }

      .pdf-content h3 {
        color: #2563eb;
        margin-top: 25px;
        margin-bottom: 15px;
        font-weight: 600;
        font-size: 18px;
      }

      .chart-image {
        max-width: 100%;
        height: auto;
        margin: 20px 0;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      .comparison-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin: 25px 0;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      .comparison-table th, .comparison-table td {
        border: 1px solid #e5e7eb;
        padding: 12px 15px;
        text-align: left;
      }

      .comparison-table th {
        background: linear-gradient(to right, #2563eb, #3b82f6);
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 12px;
        letter-spacing: 0.5px;
      }

      .comparison-table tr:nth-child(even) {
        background-color: #f8fafc;
      }

      .metric-label {
        font-weight: 600;
        color: #334155;
      }

      .metric-value {
        text-align: right;
        font-family: 'Courier New', monospace;
        font-weight: 500;
      }

      .recommendation-card {
        background-color: #f0f9ff;
        border-left: 4px solid #3b82f6;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      .recommendation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }

      .structure-badge {
        background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 14px;
        box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3);
      }

      .profit-value {
        font-weight: 700;
        color: #16a34a;
        font-size: 18px;
      }

      .tax-burden-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
        margin: 20px 0;
      }

      .tax-burden-card {
        background-color: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 15px;
        display: flex;
        flex-direction: column;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: transform 0.2s ease;
      }

      .tax-burden-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      .tax-burden-card .metric-label {
        margin-bottom: 8px;
        font-size: 14px;
        color: #64748b;
      }

      .tax-burden-card .metric-value {
        font-size: 18px;
        font-weight: 700;
        text-align: left;
      }

      .breakpoints-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        margin: 20px 0;
      }

      .breakpoint-card {
        background-color: white;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
      }

      .breakpoint-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      }

      .breakpoint-title {
        font-weight: 600;
        display: block;
        margin-bottom: 8px;
        color: #64748b;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .breakpoint-value {
        color: #2563eb;
        font-weight: 700;
        font-size: 24px;
        display: block;
        margin-bottom: 8px;
      }

      .footer {
        margin-top: 40px;
        text-align: center;
        font-size: 12px;
        color: #64748b;
        border-top: 1px solid #e2e8f0;
        padding-top: 20px;
      }

      .highlight {
        background-color: #f0fdf4;
        font-weight: 600;
      }

      .text-red {
        color: #dc2626;
      }

      .text-green {
        color: #16a34a;
      }

      .alert-warning {
        background-color: #fffbeb;
        border: 1px solid #fef3c7;
        border-left: 4px solid #f59e0b;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      .alert-warning h4 {
        color: #b45309;
        margin-top: 0;
        margin-bottom: 10px;
        font-weight: 600;
        font-size: 16px;
      }

      .alert-warning p {
        color: #78350f;
        margin: 0;
      }

      .alert-danger {
        background-color: #fef2f2;
        border: 1px solid #fee2e2;
        border-left: 4px solid #ef4444;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      .alert-danger h4 {
        color: #b91c1c;
        margin-top: 0;
        margin-bottom: 10px;
        font-weight: 600;
        font-size: 16px;
      }

      .alert-danger p {
        color: #7f1d1d;
        margin: 0;
      }

      .pie-charts-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        margin: 20px 0;
      }

      .pie-chart-card {
        background-color: white;
        border-radius: 12px;
        padding: 15px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      .pie-chart-card h4 {
        text-align: center;
        margin-bottom: 15px;
        color: #1e40af;
        font-weight: 600;
      }

      .page-break-before {
        page-break-before: always;
      }

      .page-break-after {
        page-break-after: always;
      }
    </style>
  `;

  // Capturar gráficos da página
  const barCharts = document.querySelectorAll('.bar-chart canvas');
  const pieCharts = document.querySelectorAll('.pie-chart canvas');

  element.innerHTML = `
    ${styles}
    <div class="pdf-container">
      <div class="pdf-header">
        <h1>SimFiscal</h1>
        <p>Simulador Fiscal Inteligente - Análise comparativa para 2025</p>

        <div class="company-info">
          <div>
            <strong>Empresa:</strong> ${companyName}
          </div>
          <div>
            <strong>Responsável:</strong> ${responsible}
          </div>
          <div>
            <strong>Data:</strong> ${new Date().toLocaleDateString('pt-PT')}
          </div>
        </div>
      </div>

      <div class="pdf-content">
        <h2>Resumo Executivo</h2>
        <p>Este relatório apresenta uma análise detalhada das diferentes estruturas jurídicas e regimes fiscais aplicáveis ao seu negócio, com base nos parâmetros fornecidos. O objetivo é identificar a estrutura mais vantajosa do ponto de vista fiscal.</p>

        ${results[0] && results[0].taxableProfit && generateVATThresholdAlert(results[0].taxableProfit + (results[0].annualCosts || 0), 'exempt53')}

        <div class="recommendation-card">
          <div class="recommendation-header">
            <span class="structure-badge">${bestStructure.structure.replace('ENI_', 'ENI ').replace('SociedadePorQuotas', 'Sociedade por Quotas')}</span>
            <span class="profit-value">Lucro Líquido: ${formatCurrency(bestStructure.netProfit)}</span>
          </div>
          ${bestStructure.variant ? `<p><strong>Variante:</strong> ${bestStructure.variant}</p>` : ''}
          <p>Esta estrutura apresenta o melhor resultado financeiro baseado nos valores simulados, com uma economia potencial de ${formatCurrency(Math.max(...results.map(r => r.netProfit)) - Math.min(...results.map(r => r.netProfit)))} em relação à opção menos vantajosa.</p>
          ${bestStructure.effectiveTaxRate ? `<p><strong>Taxa efetiva de imposto:</strong> ${formatPercent(bestStructure.effectiveTaxRate)}</p>` : ''}
        </div>

        <h3>Detalhes da Estrutura Recomendada</h3>

        <div class="tax-burden-grid" style="grid-template-columns: repeat(3, 1fr);">
          <div class="tax-burden-card">
            <span class="metric-label">Faturação Anual</span>
            <span class="metric-value">${formatCurrency(bestStructure.taxableProfit + (bestStructure.annualCosts || 0))}</span>
          </div>

          <div class="tax-burden-card">
            <span class="metric-label">Custos Dedutíveis</span>
            <span class="metric-value">${formatCurrency(bestStructure.annualCosts || 0)}</span>
          </div>

          <div class="tax-burden-card">
            <span class="metric-label">Lucro Tributável</span>
            <span class="metric-value">${formatCurrency(bestStructure.taxableProfit)}</span>
          </div>

          <div class="tax-burden-card">
            <span class="metric-label">Imposto (IRS/IRC)</span>
            <span class="metric-value text-red">${formatCurrency(bestStructure.incomeTax)}</span>
          </div>

          <div class="tax-burden-card">
            <span class="metric-label">Segurança Social</span>
            <span class="metric-value text-red">${formatCurrency(bestStructure.socialSecurity)}</span>
          </div>

          <div class="tax-burden-card">
            <span class="metric-label">Taxa Efetiva</span>
            <span class="metric-value">${formatPercent(bestStructure.effectiveTaxRate || 0)}</span>
          </div>

          ${bestStructure.grossSalary ? `
          <div class="tax-burden-card">
            <span class="metric-label">Salário Bruto Anual</span>
            <span class="metric-value">${formatCurrency(bestStructure.grossSalary)}</span>
          </div>
          ` : ''}

          ${bestStructure.netSalary ? `
          <div class="tax-burden-card">
            <span class="metric-label">Salário Líquido Anual</span>
            <span class="metric-value text-green">${formatCurrency(bestStructure.netSalary)}</span>
          </div>
          ` : ''}

          ${bestStructure.employerSocialSecurity ? `
          <div class="tax-burden-card">
            <span class="metric-label">SS do Empregador</span>
            <span class="metric-value text-red">${formatCurrency(bestStructure.employerSocialSecurity)}</span>
          </div>
          ` : ''}

          ${bestStructure.employeeSocialSecurity ? `
          <div class="tax-burden-card">
            <span class="metric-label">SS do Empregado</span>
            <span class="metric-value text-red">${formatCurrency(bestStructure.employeeSocialSecurity)}</span>
          </div>
          ` : ''}
        </div>

        <h3>Detalhes de IVA</h3>
        <div class="tax-burden-grid" style="grid-template-columns: repeat(3, 1fr);">
          <div class="tax-burden-card">
            <span class="metric-label">IVA Liquidado</span>
            <span class="metric-value">${formatCurrency(bestStructure.vatPaid)}</span>
          </div>

          <div class="tax-burden-card">
            <span class="metric-label">IVA Dedutível</span>
            <span class="metric-value">${formatCurrency(bestStructure.vatDeductible)}</span>
          </div>

          <div class="tax-burden-card">
            <span class="metric-label">IVA a Pagar</span>
            <span class="metric-value text-red">${formatCurrency(bestStructure.vatPaid - bestStructure.vatDeductible)}</span>
          </div>
        </div>

        <h2>Análise Comparativa</h2>

        <h3>Comparação de Estruturas Jurídicas</h3>
        <table class="comparison-table">
          <thead>
            <tr>
              <th>INDICADOR</th>
              ${results.map(r => `<th>${r.structure.replace('ENI_', 'ENI SIMPLIFIED').replace('SociedadePorQuotas', 'UNIPESSOAL')}${r.variant ? `<br><span style="font-weight: normal; font-size: 0.9em;">${r.variant}</span>` : ''}</th>`).join('')}
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="metric-label">Faturação Anual</td>
              ${results.map(r => `<td class="metric-value">${fmt.format(r.taxableProfit + r.annualCosts)}</td>`).join('')}
            </tr>
            <tr>
              <td class="metric-label">Custos Dedutíveis</td>
              ${results.map(r => `<td class="metric-value">${fmt.format(r.annualCosts)}</td>`).join('')}
            </tr>
            <tr>
              <td class="metric-label">Lucro Tributável</td>
              ${results.map(r => `<td class="metric-value">${fmt.format(r.taxableProfit)}</td>`).join('')}
            </tr>
            <tr>
              <td class="metric-label">Imposto (IRS/IRC)</td>
              ${results.map(r => `<td class="metric-value text-red">${fmt.format(r.incomeTax)}</td>`).join('')}
            </tr>
            <tr>
              <td class="metric-label">Segurança Social</td>
              ${results.map(r => `<td class="metric-value text-red">${fmt.format(r.socialSecurity)}</td>`).join('')}
            </tr>
            <tr>
              <td class="metric-label">IVA Liquidado</td>
              ${results.map(r => `<td class="metric-value">${fmt.format(r.vatPaid)}</td>`).join('')}
            </tr>
            <tr>
              <td class="metric-label">IVA Dedutível</td>
              ${results.map(r => `<td class="metric-value">${fmt.format(r.vatDeductible)}</td>`).join('')}
            </tr>
            <tr>
              <td class="metric-label">IVA a Pagar</td>
              ${results.map(r => `<td class="metric-value text-red">${fmt.format(r.vatPaid - r.vatDeductible)}</td>`).join('')}
            </tr>

            <tr>
              <td class="metric-label">Carga Fiscal Total</td>
              ${results.map(r => `<td class="metric-value text-red">${fmt.format(r.totalTax)}</td>`).join('')}
            </tr>
            <tr class="highlight">
              <td class="metric-label">Lucro Líquido</td>
              ${results.map(r => `<td class="metric-value text-green">${fmt.format(r.netProfit)}</td>`).join('')}
            </tr>
            <tr>
              <td class="metric-label">Taxa Efetiva</td>
              ${results.map(r => `<td class="metric-value">${r.effectiveTaxRate ? r.effectiveTaxRate.toFixed(1) : '0.0'}%</td>`).join('')}
            </tr>
            ${results.some(r => r.grossSalary) ? `
            <tr>
              <td class="metric-label">Salário Bruto Anual</td>
              ${results.map(r => `<td class="metric-value">${formatCurrency(r.grossSalary || 0)}</td>`).join('')}
            </tr>
            ` : ''}
            ${results.some(r => r.netSalary) ? `
            <tr>
              <td class="metric-label">Salário Líquido Anual</td>
              ${results.map(r => `<td class="metric-value text-green">${formatCurrency(r.netSalary || 0)}</td>`).join('')}
            </tr>
            ` : ''}

          </tbody>
        </table>

        <h3>Visualização Gráfica</h3>
        <div class="chart-section">
          ${barCharts.length > 0 ? `
            <img src="${(barCharts[0] as HTMLCanvasElement)?.toDataURL()}" alt="Comparação de Rendimento Líquido" class="chart-image"/>
          ` : ''}

          ${barCharts.length > 1 ? `
            <img src="${(barCharts[1] as HTMLCanvasElement)?.toDataURL()}" alt="Comparação de Carga Fiscal" class="chart-image"/>
          ` : ''}
        </div>

        <h3>Distribuição da Carga Fiscal por Estrutura</h3>
        <div class="pie-charts-grid">
          ${Array.from(pieCharts).map((canvas, index) => `
            <div class="pie-chart-card">
              <h4>${results[index].structure.replace('ENI_', 'ENI ').replace('SociedadePorQuotas', 'Sociedade por Quotas')}</h4>
              <img src="${(canvas as HTMLCanvasElement).toDataURL()}" alt="Distribuição ${results[index].structure}" class="chart-image"/>
            </div>
          `).join('')}
        </div>

        <h2>Considerações Importantes</h2>

        <h3>Parâmetros Fiscais Utilizados</h3>
        <div class="breakpoints-grid">
          <div class="breakpoint-card">
            <span class="breakpoint-title">Limite IVA</span>
            <span class="breakpoint-value">€${(results[0].vatThreshold || 14500).toLocaleString('pt-PT')}</span>
            <p>Limite para regime de isenção do artigo 53º do CIVA</p>
          </div>

          <div class="breakpoint-card">
            <span class="breakpoint-title">Coeficiente (Serviços)</span>
            <span class="breakpoint-value">${(results[0].serviceCoefficient || 0.70) * 100}%</span>
            <p>Coeficiente aplicado ao rendimento bruto no regime simplificado</p>
          </div>

          <div class="breakpoint-card">
            <span class="breakpoint-title">Taxa de IRS</span>
            <span class="breakpoint-value">Tabelas 2025</span>
            <p>Taxas progressivas conforme escalões de rendimento</p>
          </div>

          <div class="breakpoint-card">
            <span class="breakpoint-title">Taxa de IRC</span>
            <span class="breakpoint-value">20% / 16%</span>
            <p>Taxa geral / Taxa para PMEs (até €50.000)</p>
          </div>



          <div class="breakpoint-card">
            <span class="breakpoint-title">Segurança Social</span>
            <span class="breakpoint-value">Taxas 2025</span>
            <p>Trabalhadores independentes: 21,4% / 23,75%</p>
          </div>
        </div>

        <h3>Fatores Adicionais a Considerar</h3>
        <ul>
          <li><strong>Complexidade Administrativa:</strong> Sociedades requerem mais obrigações contabilísticas e declarativas comparado com empresários em nome individual.</li>
          <li><strong>Responsabilidade:</strong> Em sociedades, a responsabilidade é limitada ao capital social, enquanto ENI responde com património pessoal.</li>
          <li><strong>Perspetiva de Longo Prazo:</strong> Considere a evolução esperada do negócio nos próximos anos, pois a estrutura ideal pode mudar com o crescimento.</li>
        </ul>

        <div class="footer">
          <p>Este relatório foi gerado automaticamente pelo SimFiscal com base na legislação fiscal portuguesa para 2025.</p>
          <p>Os valores apresentados são meramente indicativos e não substituem o aconselhamento profissional.</p>
          <p>© ${new Date().getFullYear()} SimFiscal - Simulador Fiscal Inteligente | Atualizado com a legislação fiscal 2025</p>
        </div>
      </div>
    </div>
  `;

  const options = {
    margin: [15, 10, 15, 10], // top, left, bottom, right
    filename: 'simulacao-fiscal.pdf',
    image: { type: 'jpeg', quality: 0.98 },
    html2canvas: {
      scale: 2,
      useCORS: true,
      windowHeight: element.scrollHeight
    },
    jsPDF: {
      unit: 'mm',
      format: 'a4',
      orientation: 'portrait',
      hotfixes: ['px_scaling']
    },
    pagebreak: {
      mode: ['avoid-all', 'css', 'legacy'],
      before: '.page-break-before',
      after: '.page-break-after'
    }
  };

  await html2pdf().from(element).set(options).save();
}
