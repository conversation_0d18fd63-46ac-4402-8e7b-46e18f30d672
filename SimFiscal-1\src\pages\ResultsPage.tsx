// src/pages/ResultsPage.tsx
import { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { getSavedSimulations, deleteSimulation, SavedSimulation } from '../services/storageService';
import { formatCurrency, formatPercent } from '../utils/formatters';
import { BarChart } from '../charts/BarChart';
import { PieChart } from '../charts/PieChart';
import { exportToPDF } from '../services/pdfExport';
import FiscalDashboard from '../components/FiscalDashboard';
import FiscalRecommendations from '../components/FiscalRecommendations';
import ComparisonTable from '../components/ComparisonTable';
import VATThresholdAlert from '../components/VATThresholdAlert';

// Função para formatar o nome da estrutura
const formatStructureName = (structure: string): string => {
  return structure
    .replace('ENI_Simplified', 'ENI Simplificado')
    .replace('ENI_Organized', 'ENI Contabilidade Organizada')
    .replace('SociedadePorQuotas', 'Sociedade por Quotas')
    .replace('Unipessoal', 'Sociedade Unipessoal');
};

export function ResultsPage() {
  const [savedSimulations, setSavedSimulations] = useState<SavedSimulation[]>([]);
  const [selectedSimulation, setSelectedSimulation] = useState<SavedSimulation | null>(null);
  const [vatThreshold, setVatThreshold] = useState(15000);
  const [serviceCoefficient, setServiceCoefficient] = useState(0.75);
  const resultsContainerRef = useRef<HTMLDivElement>(null);
  const location = useLocation();

  // Formatador para valores monetários
  const fmt = {
    format: (value: number) => formatCurrency(value)
  };

  useEffect(() => {
    // Carregar simulações salvas quando a página for montada
    const simulations = getSavedSimulations();
    setSavedSimulations(simulations);

    // Selecionar automaticamente a simulação mais recente
    if (simulations.length > 0) {
      // Ordenar por data (mais recente primeiro)
      const sortedSimulations = [...simulations].sort(
        (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
      );
      setSelectedSimulation(sortedSimulations[0]);

      // Verificar se há um parâmetro de consulta 'scrollToResults'
      const searchParams = new URLSearchParams(location.search);
      if (searchParams.get('scrollToResults') === 'true') {
        // Rolar para o topo da seção de resultados após um pequeno atraso para garantir que o componente foi renderizado
        setTimeout(() => {
          if (resultsContainerRef.current) {
            resultsContainerRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }, 300);
      }
    }
  }, [location.search]);

  const handleDelete = (id: string) => {
    if (window.confirm('Tem certeza que deseja excluir esta simulação?')) {
      deleteSimulation(id);
      setSavedSimulations(getSavedSimulations());
      if (selectedSimulation?.id === id) {
        setSelectedSimulation(null);
      }
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="text-center max-w-3xl mx-auto mb-16">
        <span className="inline-block px-3 py-1 rounded-full bg-blue-100 text-blue-600 text-sm font-medium mb-3">Histórico de Simulações</span>
        <h1 className="text-3xl md:text-4xl font-bold mb-4">Resultados Guardados</h1>
        <p className="text-xl text-gray-600">Consulte e compare as simulações que realizou anteriormente</p>
      </div>

      {savedSimulations.length === 0 ? (
        <div className="bg-white rounded-2xl shadow-lg p-8 text-center max-w-2xl mx-auto">
          <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-10 h-10 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Nenhuma simulação encontrada</h2>
          <p className="text-gray-600 mb-8">Você ainda não realizou nenhuma simulação ou todas foram excluídas.</p>
          <Link to="/simulation" className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors">
            <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Criar Nova Simulação
          </Link>
        </div>
      ) : (
        <div className="grid md:grid-cols-3 gap-8">
          <div className="md:col-span-1">
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 sticky top-24">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6">
                <h2 className="text-xl font-bold">Simulações Guardadas</h2>
                <p className="text-blue-100 text-sm mt-1">Selecione uma simulação para ver os detalhes</p>
              </div>
              <div className="p-4">
                <Link to="/simulation" className="block w-full mb-6 px-4 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors text-center font-semibold">
                  Nova Simulação
                </Link>
                <div className="space-y-3 max-h-[500px] overflow-y-auto pr-2">
                  {savedSimulations.map((simulation) => (
                    <div
                      key={simulation.id}
                      className={`p-4 rounded-xl cursor-pointer transition-colors ${
                        selectedSimulation?.id === simulation.id
                          ? 'bg-blue-50 border-2 border-blue-500'
                          : 'bg-gray-50 border border-gray-200 hover:bg-gray-100'
                      }`}
                      onClick={() => setSelectedSimulation(simulation)}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-semibold text-gray-800">{simulation.companyName}</h3>
                          <p className="text-sm text-gray-500">{formatDate(simulation.date)}</p>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDelete(simulation.id);
                          }}
                          className="text-red-500 hover:text-red-700 p-1"
                          title="Excluir simulação"
                        >
                          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                      <div className="mt-2 text-sm">
                        <p className="text-gray-600">Responsável: {simulation.responsible}</p>
                        <p className="text-gray-600 mt-1">
                          {simulation.results.length} estruturas analisadas
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="md:col-span-2">
            {selectedSimulation ? (
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100" ref={resultsContainerRef}>
                <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6">
                  <div className="flex justify-between items-center">
                    <h2 className="text-xl font-bold">{selectedSimulation.companyName}</h2>
                    <span className="px-3 py-1 bg-white/20 rounded-lg text-sm">
                      {formatDate(selectedSimulation.date)}
                    </span>
                  </div>
                  <p className="text-blue-100 text-sm mt-1">Responsável: {selectedSimulation.responsible}</p>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-bold mb-4 text-gray-800">Resultados da Simulação</h3>

                  {/* Dashboard Fiscal */}
                  <FiscalDashboard results={selectedSimulation.results} />

                  {/* Recomendações Fiscais */}
                  <FiscalRecommendations
                    params={{
                      activityType: 'services',
                      vatRegime: 'normalMonthly',
                      vatType: 'normal',
                      legalStructures: selectedSimulation.results.map(r => r.structure),
                      hasOtherSocialSecurity: false,
                      isFirstYear: false,
                      annualRevenue: selectedSimulation.results[0].taxableProfit + selectedSimulation.results[0].annualCosts!,
                      annualCosts: selectedSimulation.results[0].annualCosts || 0
                    }}
                    results={selectedSimulation.results}
                  />

                  {/* Resumo das Estruturas */}
                  <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 mb-8">
                    <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4">
                      <h4 className="text-lg font-bold">Resumo das Estruturas</h4>
                    </div>
                    <div className="p-4">
                      <div className="overflow-x-auto">
                        <table className="w-full table-auto">
                          <thead>
                            <tr className="bg-gray-50">
                              <th className="text-left p-3 font-semibold text-gray-700 border-b">Estrutura</th>
                              <th className="text-right p-3 font-semibold text-gray-700 border-b">Faturação</th>
                              <th className="text-right p-3 font-semibold text-gray-700 border-b">Custos</th>
                              <th className="text-right p-3 font-semibold text-gray-700 border-b">Impostos</th>
                              <th className="text-right p-3 font-semibold text-gray-700 border-b">Lucro Líquido</th>
                            </tr>
                          </thead>
                          <tbody>
                            {selectedSimulation.results.map((result, index) => {
                              // Formatar o nome da estrutura de forma mais clara
                              const structureName = result.structure
                                .replace('ENI_Simplified', 'ENI Simplificado')
                                .replace('ENI_Organized', 'ENI Contabilidade Organizada')
                                .replace('SociedadePorQuotas', 'Sociedade por Quotas')
                                .replace('Unipessoal', 'Sociedade Unipessoal');

                              // Extrair apenas a parte relevante da variante (sem "Recomendado: ")
                              const variantText = result.variant ? result.variant.replace('Recomendado: ', '') : '';

                              return (
                                <tr key={index} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} ${result.recommended ? 'bg-green-50' : ''} hover:bg-blue-50 transition-colors`}>
                                  <td className="p-3 font-medium border-b">
                                    <div>
                                      {structureName}
                                      {variantText && (
                                        <span className="block text-sm text-gray-500 mt-1">
                                          {variantText}
                                        </span>
                                      )}
                                      {result.recommended && (
                                        <span className="inline-block px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded-full mt-1">
                                          Recomendado
                                        </span>
                                      )}
                                    </div>
                                  </td>
                                  <td className="p-3 text-right border-b">
                                    {formatCurrency(result.taxableProfit + (result.annualCosts || 0))}
                                  </td>
                                  <td className="p-3 text-right border-b">
                                    {formatCurrency(result.annualCosts || 0)}
                                  </td>
                                  <td className="p-3 text-right text-red-600 border-b">
                                    {formatCurrency(result.totalTax)}
                                  </td>
                                  <td className="p-3 text-right font-bold border-b">
                                    <span className={result.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}>
                                      {formatCurrency(result.netProfit)}
                                    </span>
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>

                  {/* Gráficos */}
                  <div className="grid md:grid-cols-2 gap-8 mt-8 mb-8">
                    <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
                      <div className="bg-gradient-to-r from-green-600 to-green-700 text-white p-6">
                        <h3 className="text-xl font-bold">Lucro Líquido por Estrutura</h3>
                        <p className="text-green-100 text-sm mt-1">Comparação do lucro líquido entre as diferentes estruturas</p>
                      </div>
                      <div className="p-6 bar-chart">
                        <BarChart
                          data={selectedSimulation.results.map((r, index) => ({
                            label: `Simulação ${index + 1}${r.recommended ? ' ★' : ''}`,
                            value: r.netProfit
                          }))}
                          yAxisLabel="Euros (€)"
                          title="Comparação de Lucro Líquido"
                        />
                      </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
                      <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
                        <h3 className="text-xl font-bold">Taxa Efetiva de Imposto</h3>
                        <p className="text-blue-100 text-sm mt-1">Comparação da taxa efetiva entre as diferentes estruturas</p>
                      </div>
                      <div className="p-6 bar-chart">
                        <BarChart
                          data={selectedSimulation.results.map((r, index) => ({
                            label: `Simulação ${index + 1}${r.recommended ? ' ★' : ''}`,
                            value: r.effectiveTaxRate || 0
                          }))}
                          yAxisLabel="Percentagem (%)"
                          title="Comparação de Taxa Efetiva"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Gráfico de Comparação de Rendimentos vs. Impostos */}
                  <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 mb-8">
                    <div className="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-6">
                      <h3 className="text-xl font-bold">Comparação de Rendimentos vs. Impostos</h3>
                      <p className="text-purple-100 text-sm mt-1">Análise da relação entre rendimento líquido e carga fiscal</p>
                    </div>
                    <div className="p-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                          <h4 className="text-lg font-semibold text-gray-700 mb-4">Estrutura Recomendada</h4>
                          <div className="bg-gray-50 p-4 rounded-lg mb-4">
                            <p className="font-medium text-gray-700">
                              {formatStructureName(selectedSimulation.results.find(r => r.recommended)?.structure || '')}
                              {selectedSimulation.results.find(r => r.recommended)?.variant && (
                                <span className="block text-sm text-gray-500 mt-1">
                                  {selectedSimulation.results.find(r => r.recommended)?.variant?.replace('Recomendado: ', '')}
                                </span>
                              )}
                            </p>
                          </div>
                          <div className="space-y-3">
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Faturação Anual:</span>
                              <span className="font-medium">
                                {formatCurrency((selectedSimulation.results.find(r => r.recommended)?.taxableProfit || 0) +
                                              (selectedSimulation.results.find(r => r.recommended)?.annualCosts || 0))}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Custos Dedutíveis:</span>
                              <span className="font-medium">
                                {formatCurrency(selectedSimulation.results.find(r => r.recommended)?.annualCosts || 0)}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Total de Impostos:</span>
                              <span className="font-medium text-red-600">
                                {formatCurrency(selectedSimulation.results.find(r => r.recommended)?.totalTax || 0)}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Lucro Líquido:</span>
                              <span className="font-medium text-green-600">
                                {formatCurrency(selectedSimulation.results.find(r => r.recommended)?.netProfit || 0)}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="pie-chart">
                          <PieChart
                            data={[
                              {
                                label: 'Lucro Líquido',
                                value: selectedSimulation.results.find(r => r.recommended)?.netProfit || 0
                              },
                              {
                                label: 'Impostos e Contribuições',
                                value: selectedSimulation.results.find(r => r.recommended)?.totalTax || 0
                              },
                              {
                                label: 'Custos Operacionais',
                                value: selectedSimulation.results.find(r => r.recommended)?.annualCosts || 0
                              }
                            ]}
                            title="Distribuição da Faturação na Simulação Recomendada"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Comparação de Estruturas */}
                  <h3 className="text-xl font-bold mb-4 text-gray-800 mt-8">Comparação de Estruturas</h3>
                  <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 mb-8">
                    <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4">
                      <h4 className="text-lg font-bold">Resumo Comparativo</h4>
                    </div>
                    <div className="p-6">
                      <div className="overflow-x-auto">
                        <table className="w-full table-auto">
                          <thead>
                            <tr className="bg-gray-50">
                              <th className="text-center p-3 font-semibold text-gray-700 border-b" style={{ width: '40px' }}>#</th>
                              <th className="text-left p-3 font-semibold text-gray-700 border-b">Estrutura</th>
                              <th className="text-right p-3 font-semibold text-gray-700 border-b">Faturação</th>
                              <th className="text-right p-3 font-semibold text-gray-700 border-b">Impostos</th>
                              <th className="text-right p-3 font-semibold text-gray-700 border-b">Taxa Efetiva</th>
                              <th className="text-right p-3 font-semibold text-gray-700 border-b">Lucro Líquido</th>
                            </tr>
                          </thead>
                          <tbody>
                            {selectedSimulation.results.map((result, index) => (
                              <tr key={index} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} ${result.recommended ? 'bg-green-50' : ''}`}>
                                <td className="p-3 text-center font-bold border-b">
                                  <div className={`w-8 h-8 rounded-full ${result.recommended ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'} flex items-center justify-center mx-auto`}>
                                    {index + 1}
                                  </div>
                                </td>
                                <td className="p-3 font-medium border-b">
                                  {result.structure.replace('ENI_', 'ENI ').replace('SociedadePorQuotas', 'Lda.')}
                                  {result.variant && (
                                    <span className="block text-sm text-gray-500 mt-1">{result.variant}</span>
                                  )}
                                  {result.recommended && (
                                    <span className="inline-block px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded-full mt-1">
                                      Recomendado
                                    </span>
                                  )}
                                </td>
                                <td className="p-3 text-right border-b">
                                  {formatCurrency(result.taxableProfit + (result.annualCosts || 0))}
                                </td>
                                <td className="p-3 text-right text-red-600 border-b">
                                  {formatCurrency(result.totalTax)}
                                </td>
                                <td className="p-3 text-right border-b">
                                  {(result.effectiveTaxRate || 0).toFixed(1)}%
                                </td>
                                <td className="p-3 text-right font-bold border-b">
                                  <span className={result.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}>
                                    {formatCurrency(result.netProfit)}
                                  </span>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>

                  {/* Botões de ação */}
                  <div className="mt-8 flex justify-between">
                    <button
                      onClick={() => exportToPDF(selectedSimulation.results, selectedSimulation.companyName, selectedSimulation.responsible)}
                      className="px-6 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-colors flex items-center"
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                      Exportar PDF
                    </button>
                    <Link to="/simulation" className="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors">
                      Nova Simulação
                    </Link>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-gray-50 rounded-2xl border border-gray-200 p-12 text-center flex flex-col items-center justify-center h-full">
                <svg className="w-20 h-20 text-gray-400 mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 className="text-xl font-semibold text-gray-600 mb-2">Selecione uma Simulação</h3>
                <p className="text-gray-500 max-w-md">
                  Escolha uma simulação da lista à esquerda para visualizar os resultados detalhados.
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
