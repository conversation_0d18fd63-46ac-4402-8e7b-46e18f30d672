
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom background pattern */
.bg-grid-pattern {
  background-image:
    linear-gradient(to right, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

@layer base {
  :root {
    --primary: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary: #64748b;
    --success: #059669;
    --danger: #dc2626;
    --background: #f8fafc;
    --surface: #ffffff;
    --text: #0f172a;
    --text-light: #64748b;
    --border: #e2e8f0;
  }

  body {
    font-family: 'Poppins', -apple-system, sans-serif;
    background-color: var(--background);
    color: var(--text);
    line-height: 1.5;
  }
}

@layer components {
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  .form-input, .form-select {
    @apply w-full border rounded-lg p-3 bg-white shadow-sm transition duration-200;
  }

  .form-input:hover, .form-select:hover {
    @apply border-blue-300;
  }

  .form-input:focus, .form-select:focus {
    @apply outline-none ring-2 ring-blue-500 border-blue-500;
  }

  .steps-container {
    @apply flex justify-between items-center mb-8 relative;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 40px;
  }

  .steps-container::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background: #e2e8f0;
    top: 50%;
    transform: translateY(-50%);
    z-index: 0;
  }

  .step-button {
    @apply w-16 h-16 rounded-full flex items-center justify-center text-lg font-bold transition-all duration-300;
    position: relative;
    z-index: 1;
    border: 3px solid transparent;
  }

  .step-button.active {
    @apply bg-gradient-to-r from-blue-500 to-blue-600 text-white;
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.3), 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transform: scale(1.15);
  }

  .step-button.completed {
    @apply bg-green-600 text-white;
    box-shadow: 0 0 0 4px rgba(22, 163, 74, 0.2);
  }

  .step-button:not(.active):not(.completed) {
    @apply bg-white text-gray-600 border-gray-200;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .step-button:hover:not(.active):not(.completed) {
    @apply bg-gray-50 border-gray-300;
    transform: translateY(-2px);
  }

  .step-button.completed:hover {
    transform: scale(1.05);
  }

  .form-group {
    @apply space-y-2;
  }

  .form-group label {
    @apply block font-medium;
  }
}

@layer components {
  /* Forms */
  form {
    @apply bg-white p-8 rounded-xl shadow-lg border border-gray-200;
  }

  .form-group {
    @apply mb-6;
  }

  label {
    @apply block font-semibold mb-2 text-gray-700 text-sm;
  }

  input, select {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 hover:bg-white transition-colors duration-200;
  }

  button {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-200 cursor-pointer shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
  }

  /* Tables */
  .comparison-table {
    @apply w-full border-collapse bg-white rounded-xl overflow-hidden shadow-lg;
  }

  .comparison-table th,
  .comparison-table td {
    @apply p-5 text-left border-b border-gray-200;
  }

  .comparison-table th {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold uppercase text-sm tracking-wider;
  }

  .comparison-table tr:hover {
    @apply bg-blue-50 transition-colors duration-150;
  }

  .comparison-table tr:last-child td {
    @apply border-b-0;
  }

  .comparison-table tr:nth-child(even) {
    @apply bg-gray-50;
  }

  /* Charts */
  .chart-container {
    @apply bg-white p-8 rounded-xl shadow-lg border border-gray-200 my-8 transition-all duration-300 hover:shadow-xl;
  }

  .chart-title {
    @apply text-xl font-bold mb-6 text-blue-700 border-b-2 border-blue-100 pb-3;
  }

  /* Typography */
  h1 {
    @apply text-4xl font-bold mb-8 text-gray-800 leading-tight;
    background: linear-gradient(to right, #1e40af, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Exceção para h1 em fundos escuros */
  .bg-gradient-to-r h1,
  .from-blue-800 h1,
  .to-blue-900 h1,
  [style*="background"] h1 {
    background: none !important;
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: white !important;
    color: white !important;
  }

  h2 {
    @apply text-2xl font-bold my-6 text-blue-700;
  }

  h3 {
    @apply text-xl font-semibold my-4 text-gray-700;
  }

  .metric-value {
    @apply font-bold text-blue-700 text-xl;
  }

  .metric-label {
    @apply text-gray-600 text-sm font-medium;
  }

  p {
    @apply text-gray-700 leading-relaxed;
  }

  /* Error messages */
  .error-message {
    @apply text-red-600 text-sm mt-1;
  }

  /* Results Section */
  .results-section {
    @apply mt-12 p-10 bg-gradient-to-br from-blue-50 to-white rounded-2xl shadow-xl border border-blue-100;
  }

  .results-section h2 {
    @apply text-2xl font-bold mb-8 pb-4 text-blue-700 border-b-2 border-blue-200 inline-block;
  }

  .results-section h3 {
    @apply text-xl font-semibold my-6 text-blue-600 flex items-center;
  }

  .results-section h3::before {
    content: "";
    @apply w-2 h-6 bg-blue-500 rounded-full mr-3 inline-block;
  }

  /* Analysis styles */
  .analysis-container {
    @apply mt-12 bg-white rounded-xl shadow-xl border border-gray-100;
  }

  .chart-section {
    @apply bg-white p-10 rounded-xl my-10 shadow-lg border border-gray-100 transition-all duration-300 hover:shadow-xl;
  }

  .comparison-table-section {
    @apply bg-white p-10 rounded-xl my-10 shadow-lg border border-gray-100 transition-all duration-300 hover:shadow-xl;
  }

  .analysis-section {
    @apply p-10 border-b border-gray-200;
  }

  .analysis-section:last-child {
    @apply border-b-0;
  }

  .section-title {
    @apply text-2xl font-bold mb-8 text-blue-700 flex items-center;
  }

  .section-title::before {
    content: "";
    @apply w-2 h-8 bg-blue-500 rounded-full mr-3 inline-block;
  }

  .recommendation-card {
    @apply bg-gradient-to-br from-blue-50 to-white rounded-xl p-8 shadow-md border border-blue-100 transition-all duration-300 hover:shadow-lg;
  }

  .recommendation-header {
    @apply flex justify-between items-center mb-6;
  }

  .structure-badge {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white px-5 py-2 rounded-lg font-medium shadow-md;
  }

  .profit-value {
    @apply text-2xl font-bold text-green-600;
  }

  .metrics-grid {
    @apply grid grid-cols-1 md:grid-cols-3 gap-6 my-8;
  }

  .metric-card {
    @apply bg-white p-6 rounded-xl text-center shadow-md border border-gray-100 transition-all duration-300 hover:shadow-lg hover:border-blue-200;
  }

  .considerations-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-8 my-8;
  }

  .consideration-card {
    @apply bg-white p-8 rounded-xl shadow-md border border-gray-100 transition-all duration-300 hover:shadow-lg hover:border-blue-200;
  }

  .consideration-card h4 {
    @apply text-blue-700 font-bold mb-4 text-lg;
  }

  /* Chart Adjustments */
  .pie-charts-grid {
    @apply grid grid-cols-1 md:grid-cols-3 gap-8 my-10;
  }

  .pie-chart-card {
    @apply bg-white p-8 rounded-xl shadow-lg border border-gray-100 transition-all duration-300 hover:shadow-xl;
  }

  .pie-chart-card h4 {
    @apply text-lg font-bold text-blue-700 mb-4 text-center;
  }

  /* Export Button */
  .export-button {
    @apply inline-flex items-center gap-3 bg-gradient-to-r from-green-600 to-green-700 text-white px-8 py-4 rounded-lg font-bold hover:from-green-700 hover:to-green-800 transition-all duration-200 cursor-pointer my-10 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
  }
}
