// src/components/VATThresholdAlert.tsx
import React from 'react';
import { VAT_EXEMPTION_THRESHOLD, VAT_IMMEDIATE_TRANSITION_THRESHOLD } from '@types/TaxBrackets2025';
import { formatCurrency } from '../utils/formatters';

interface VATThresholdAlertProps {
  annualRevenue: number;
  vatRegime: string;
}

const VATThresholdAlert: React.FC<VATThresholdAlertProps> = ({ annualRevenue, vatRegime }) => {
  // Não mostrar alerta se não estiver no regime de isenção
  if (vatRegime !== 'exempt53') {
    return null;
  }

  // Determinar o tipo de alerta com base na faturação anual
  let alertType: 'warning' | 'danger' | null = null;
  let alertMessage = '';

  if (annualRevenue > VAT_IMMEDIATE_TRANSITION_THRESHOLD) {
    alertType = 'danger';
    alertMessage = `A faturação anual de ${formatCurrency(annualRevenue)} excede o limiar de transição imediata de ${formatCurrency(VAT_IMMEDIATE_TRANSITION_THRESHOLD)} (125% do limiar base). Isto obriga à transição imediata para o regime normal de IVA, e a fatura que excede este valor já deve incluir IVA.`;
  } else if (annualRevenue > VAT_EXEMPTION_THRESHOLD) {
    alertType = 'warning';
    alertMessage = `A faturação anual de ${formatCurrency(annualRevenue)} excede o limiar de isenção de IVA de ${formatCurrency(VAT_EXEMPTION_THRESHOLD)}. A isenção será mantida até o final do ano, mas será necessário entregar declaração de alterações no prazo de 15 dias úteis a contar do último dia do ano e passar para o regime normal a partir de 1 de janeiro do ano seguinte.`;
  }

  // Se não houver alerta a mostrar, não renderizar nada
  if (!alertType) {
    return null;
  }

  // Definir classes CSS com base no tipo de alerta
  const alertClasses = alertType === 'danger'
    ? 'bg-red-50 border-red-200 text-red-800'
    : 'bg-yellow-50 border-yellow-200 text-yellow-800';

  const iconClasses = alertType === 'danger'
    ? 'text-red-500'
    : 'text-yellow-500';

  return (
    <div className={`p-6 mb-6 rounded-xl border-2 shadow-md ${alertClasses}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {alertType === 'danger' ? (
            <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
              <svg className="w-7 h-7 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
          ) : (
            <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
              <svg className="w-7 h-7 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          )}
        </div>
        <div className="ml-4">
          <h3 className="text-xl font-bold">
            {alertType === 'danger' ? 'Atenção: Transição Imediata para Regime Normal de IVA' : 'Aviso: Limiar de Isenção de IVA Excedido'}
          </h3>
          <div className="mt-2">
            <p className="text-base">{alertMessage}</p>
            <div className="mt-4 p-3 bg-white/50 rounded-lg border border-current/20">
              <p className="font-medium">Implicações:</p>
              <ul className="list-disc ml-5 mt-1 space-y-1 text-sm">
                {alertType === 'danger' ? (
                  <>
                    <li>Obrigação de emitir faturas com IVA a partir deste momento</li>
                    <li>Necessidade de submeter declaração de alterações à AT no prazo de 15 dias</li>
                    <li>Obrigação de cumprir todas as obrigações do regime normal de IVA</li>
                  </>
                ) : (
                  <>
                    <li>Manutenção da isenção até ao final do ano civil</li>
                    <li>Necessidade de submeter declaração de alterações até 31 de janeiro do ano seguinte</li>
                    <li>Transição para o regime normal a partir de 1 de janeiro do ano seguinte</li>
                  </>
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VATThresholdAlert;
