
import React from 'react';
import type { SimulationResult } from '@types/SimulationResult';

interface Props {
  results: SimulationResult[];
}

export function DecisionAssistant({ results }: Props) {
  const getBestStructure = () => {
    return results.reduce((prev, curr) => 
      prev.netProfit > curr.netProfit ? prev : curr
    );
  };

  const getRecommendations = () => {
    const best = getBestStructure();
    const avgTaxBurden = results.reduce((acc, curr) => 
      acc + (curr.totalTax / curr.taxableProfit), 0) / results.length;

    return [
      `A estrutura ${best.structure.replace('_', ' ')} apresenta o melhor resultado líquido`,
      `Carga fiscal média: ${(avgTaxBurden * 100).toFixed(1)}%`,
      best.structure === 'ENI_Simplified' ? 'Ideal para baixa complexidade operacional' :
      best.structure === 'Unipessoal' ? 'Oferece boa proteção patrimonial' :
      'Estrutura mais robusta para crescimento'
    ];
  };

  return (
    <div className="decision-assistant">
      <h3 className="text-xl font-semibold mb-4">Assistente de Decisão</h3>
      <div className="recommendations">
        {getRecommendations().map((rec, i) => (
          <div key={i} className="recommendation-item">
            <span className="bullet">•</span>
            <p>{rec}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
