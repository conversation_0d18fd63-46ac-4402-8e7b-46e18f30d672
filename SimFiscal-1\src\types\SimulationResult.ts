// src/types/SimulationResult.ts

/** Resultado de impostos e lucro para um regime */
export interface SimulationResult {
  /** Nome do regime simulado (ex: "ENI_Simplified") */
  structure: string;
  /** Variante da estrutura (ex: "Salário Otimizado") */
  variant?: string;
  /** Lucro tributável (€) */
  taxableProfit: number;
  /** IRC ou IRS (€) */
  incomeTax: number;
  /** Taxa Adicional de Solidariedade (€) */
  solidarityTax?: number;
  /** Segurança Social (€) */
  socialSecurity: number;
  /** Segurança Social do empregador (€) */
  employerSocialSecurity?: number;
  /** Segurança Social do empregado (€) */
  employeeSocialSecurity?: number;
  /** Retenção na Fonte (€) */
  withholdingTax?: number;
  /** IVA liquidado (€) */
  vatPaid: number;
  /** IVA dedutível (€) */
  vatDeductible: number;
  /** <PERSON><PERSON><PERSON> bruto anual (€) */
  grossSalary?: number;
  /** Salário líquido anual (€) */
  netSalary?: number;
  // Campos de dividendos removidos

  /** Carga fiscal total (€) */
  totalTax: number;
  /** Lucro líquido final da empresa (€) */
  netProfit: number;
  /** Taxa efetiva de imposto (%) */
  effectiveTaxRate?: number;
  /** Custos anuais (€) */
  annualCosts?: number;
  /** Limiar de isenção de IVA (€) */
  vatThreshold?: number;
  /** Coeficiente para serviços (0-1) */
  serviceCoefficient?: number;
  /** Recomendado (boolean) */
  recommended?: boolean;
}
