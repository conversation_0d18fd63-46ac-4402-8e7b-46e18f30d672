// src/utils/generatePDF.ts
import html2pdf from 'html2pdf.js';

export const generateFiscalPDF = () => {
  const element = document.getElementById('fiscal-info');
  if (!element) return;

  const opt = {
    margin: 1,
    filename: 'SimFiscal_Parametros_2025.pdf',
    image: { type: 'jpeg', quality: 0.98 },
    html2canvas: { scale: 2 },
    jsPDF: { unit: 'cm', format: 'a4', orientation: 'portrait' }
  };

  html2pdf().set(opt).from(element).save();
};

export const generateResultsPDF = () => {
  const element = document.getElementById('results-container');
  if (!element) return;

  const opt = {
    margin: 1,
    filename: 'SimFiscal_Resultados.pdf',
    image: { type: 'jpeg', quality: 0.98 },
    html2canvas: { scale: 2 },
    jsPDF: { unit: 'cm', format: 'a4', orientation: 'portrait' }
  };

  html2pdf().set(opt).from(element).save();
};
