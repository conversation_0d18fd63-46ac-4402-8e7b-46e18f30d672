import React from 'react';
import { Chart as ChartJS, ArcElement, Toolt<PERSON>, Legend } from 'chart.js';
import { Pie } from 'react-chartjs-2';

ChartJS.register(ArcElement, Tooltip, Legend);

interface ChartDataItem {
  label: string;
  value: number;
}

interface Props {
  data: ChartDataItem[];
  title?: string;
}

export function PieChart({ data, title }: Props) {
  // Cores mais atraentes e significativas
  const chartData = {
    labels: data.map(item => item.label),
    datasets: [
      {
        data: data.map(item => item.value),
        backgroundColor: [
          'rgba(34, 197, 94, 0.8)',  // Verde para lucro líquido
          'rgba(239, 68, 68, 0.8)',  // Vermelho para impostos
          'rgba(59, 130, 246, 0.8)', // Azul para custos operacionais
          'rgba(168, 85, 247, 0.8)', // Roxo (caso haja mais categorias)
          'rgba(251, 191, 36, 0.8)', // <PERSON><PERSON> (caso haja mais categorias)
        ],
        borderColor: [
          'rgba(21, 128, 61, 1)',   // Verde escuro
          'rgba(185, 28, 28, 1)',   // Vermelho escuro
          'rgba(37, 99, 235, 1)',   // Azul escuro
          'rgba(126, 34, 206, 1)',  // Roxo escuro
          'rgba(217, 119, 6, 1)',   // Amarelo escuro
        ],
        borderWidth: 2,
        hoverOffset: 15,
        hoverBorderWidth: 3,
      },
    ],
  };

  // Calcular o total para mostrar percentagens
  const total = data.reduce((sum, item) => sum + item.value, 0);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 20,
          usePointStyle: true,
          pointStyle: 'circle',
          font: {
            size: 12,
            weight: 'bold'
          },
          color: '#334155', // Cinza escuro
          generateLabels: function(chart) {
            const datasets = chart.data.datasets;
            return chart.data.labels.map((label, i) => {
              const value = datasets[0].data[i] as number;
              const percentage = ((value / total) * 100).toFixed(1);
              return {
                text: `${label} (${percentage}%)`,
                fillStyle: datasets[0].backgroundColor[i],
                strokeStyle: datasets[0].borderColor[i],
                lineWidth: 2,
                hidden: false,
                index: i
              };
            });
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw as number;
            const label = context.label || '';
            const percentage = ((value / total) * 100).toFixed(1);
            const formattedValue = new Intl.NumberFormat('pt-PT', {
              style: 'currency',
              currency: 'EUR'
            }).format(value);

            return `${label}: ${formattedValue} (${percentage}%)`;
          }
        },
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        padding: 12,
        titleFont: {
          size: 14,
          weight: 'bold'
        },
        bodyFont: {
          size: 14
        },
        cornerRadius: 6
      },
      title: {
        display: !!title,
        text: title || '',
        font: {
          size: 16,
          weight: 'bold'
        },
        padding: {
          bottom: 20
        },
        color: '#1e3a8a' // Azul escuro
      }
    }
  };

  return (
    <div style={{ height: '240px' }}>
      <Pie data={chartData} options={options} />
    </div>
  );
}
