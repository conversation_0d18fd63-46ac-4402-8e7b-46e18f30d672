import { SimulationResult } from '../types/SimulationResult';

export interface SavedSimulation {
  id: string;
  date: string;
  companyName: string;
  responsible: string;
  results: SimulationResult[];
}

const STORAGE_KEY = 'simfiscal_saved_simulations';

// Salvar uma nova simulação
export function saveSimulation(
  companyName: string,
  responsible: string,
  results: SimulationResult[]
): SavedSimulation {
  const savedSimulations = getSavedSimulations();
  
  const newSimulation: SavedSimulation = {
    id: Date.now().toString(),
    date: new Date().toISOString(),
    companyName,
    responsible,
    results
  };
  
  savedSimulations.push(newSimulation);
  localStorage.setItem(STORAGE_KEY, JSON.stringify(savedSimulations));
  
  return newSimulation;
}

// Obter todas as simulações salvas
export function getSavedSimulations(): SavedSimulation[] {
  const storedData = localStorage.getItem(STORAGE_KEY);
  if (!storedData) return [];
  
  try {
    return JSON.parse(storedData);
  } catch (error) {
    console.error('Erro ao carregar simulações:', error);
    return [];
  }
}

// Obter uma simulação específica pelo ID
export function getSimulationById(id: string): SavedSimulation | null {
  const savedSimulations = getSavedSimulations();
  return savedSimulations.find(sim => sim.id === id) || null;
}

// Excluir uma simulação
export function deleteSimulation(id: string): boolean {
  const savedSimulations = getSavedSimulations();
  const filteredSimulations = savedSimulations.filter(sim => sim.id !== id);
  
  if (filteredSimulations.length === savedSimulations.length) {
    return false; // Nenhuma simulação foi removida
  }
  
  localStorage.setItem(STORAGE_KEY, JSON.stringify(filteredSimulations));
  return true;
}
